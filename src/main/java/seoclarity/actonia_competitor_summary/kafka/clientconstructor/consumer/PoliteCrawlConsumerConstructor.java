package seoclarity.actonia_competitor_summary.kafka.clientconstructor.consumer;

import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.actonia_competitor_summary.kafka.properties.PoliteCrawlKafkaProperties;

import java.util.Collection;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025/1/20 15:57
 */
public class PoliteCrawlConsumerConstructor {

    private final static Logger log = LogManager.getLogger(PoliteCrawlConsumerConstructor.class.getName());

    private static PoliteCrawlConsumerConstructor politeCrawlConsumerConstructor;

    public static PoliteCrawlConsumerConstructor getInstance() {
        if (politeCrawlConsumerConstructor == null) {
            politeCrawlConsumerConstructor = new PoliteCrawlConsumerConstructor();
        }
        return politeCrawlConsumerConstructor;
    }

    /**
     * use default page crawler group
     * @return
     */
    public KafkaConsumer<String, String> buildKafkaConsumer(){
        final Properties props = PoliteCrawlKafkaProperties.getInstance().buildConsumerProperties();
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);
        return kafkaConsumer;
    }

    /**
     * use default page crawler group and provided topics
     * @return
     */
    public Consumer<String, String> buildKafkaConsumerWithTopics(Collection<String> topicsName) {
        if (topicsName.isEmpty()) {
            log.error("please provide topic...");
            return null;
        }
        final Properties props = PoliteCrawlKafkaProperties.getInstance().buildConsumerProperties();
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(topicsName);
        log.info("build kafka consumer with topic: " + topicsName);
        return kafkaConsumer;
    }

    /**
     * use provided page crawler group and topics
     * @return
     */
    public Consumer<String, String> buildKafkaConsumerWithTopicsAndGroup(Collection<String> topicsNames, String groupName) {
        if (topicsNames.isEmpty()) {
            log.error("please provide topic...");
            return null;
        }
        final Properties props = PoliteCrawlKafkaProperties.getInstance().buildConsumerProperties(groupName);
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(topicsNames);
        log.info("build kafka consumer with topic: " + topicsNames);
        return kafkaConsumer;
    }

}
