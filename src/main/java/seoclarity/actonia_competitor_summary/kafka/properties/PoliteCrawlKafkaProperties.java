package seoclarity.actonia_competitor_summary.kafka.properties;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025/1/20 15:58
 */
public class PoliteCrawlKafkaProperties {

    private final static Properties prop = PlatformPropertiesLoader.getInstance().getProperties();

    private static PoliteCrawlKafkaProperties pcPropIns;

    public static PoliteCrawlKafkaProperties getInstance() {
        if (pcPropIns == null) {
            pcPropIns = new PoliteCrawlKafkaProperties();
        }
        return pcPropIns;
    }

    public Properties buildConsumerBaseProperties() {
        Properties kafkaProps = new Properties();
        kafkaProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, prop.getProperty("polite.crawl.consumer.kafka.bootstrap.internal.servers"));
        kafkaProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, prop.getProperty("polite.crawl.consumer.max.poll.records"));
        kafkaProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, prop.getProperty("polite.crawl.consumer.fetch.min.bytes"));
        kafkaProps.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, prop.getProperty("polite.crawl.consumer.fetch.max.bytes"));
        kafkaProps.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, prop.getProperty("polite.crawl.consumer.max.partition.fetch.bytes"));
        kafkaProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, prop.getProperty("polite.crawl.consumer.fetch.max.wait.ms"));
        kafkaProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, prop.getProperty("polite.crawl.consumer.auto.commit"));
        kafkaProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, prop.getProperty("polite.crawl.consumer.auto.offset.reset", "earliest"));

        //SASL_PLAINTEXT
        kafkaProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        kafkaProps.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=se0Clarity99 password=LmNoPq123rStUv456WxYzAbC789dEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpOSOFJI0k;");
        kafkaProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        return kafkaProps;
    }


    public Properties buildConsumerProperties(String groupName) {
        final Properties properties = buildConsumerBaseProperties();
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, groupName);
        return properties;
    }

    public Properties buildConsumerProperties() {
        final Properties properties = buildConsumerBaseProperties();
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, prop.getProperty("polite.crawl.consumer.kafka.group.id"));
        return properties;
    }

}
