package seoclarity.actonia_competitor_summary.upload.aiscraper;

import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import seoclarity.actonia_competitor_summary.entity.clarityDB.aiscraper.AiScraperResponse;
import seoclarity.actonia_competitor_summary.entity.clarityDB.aiscraper.AiScraperResponseIntermediate;

import java.io.IOException;
import java.net.*;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025/4/14 11:31
 */
public class ValidateAiScraperKafka {

    private static final List<String> topicList = Arrays.asList(AiScraperResponseIntermediate.TOPIC_AI_INSIGHTS, AiScraperResponse.TOPIC_AI_INSIGHTS_BRAND);
//    private static final List<String> contrastTopicList = Arrays.asList("ai_insights_brand_test");
    private static final List<String> contrastTopicList = Arrays.asList(AiScraperResponse.TOPIC_AI_INSIGHTS_BRAND);
    private static final List<Integer> keywordIdList = Arrays.asList(33968, 34020, 34013, 34023, 33946, 34027);
//    private static final List<Integer> usIdList = Arrays.asList(98468, 98330, 98171, 66407, 98279, 99743, 98033, 98414, 99695, 98333);
//    private static final List<Integer> ukIdList = Arrays.asList(112049, 111953, 112169, 111980, 111926, 111932, 111923, 112193, 111989);
//    private static final List<Integer> allIdList = Arrays.asList(98468, 98330, 98171, 66407, 98279, 99743, 98033, 98414, 99695, 98333, 112049, 111953, 112169, 111980, 111926, 111932, 111923, 112193, 111989);
    private static final DateTimeFormatter DATE_MILLI_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static void main(String[] args) {

        ValidateAiScraperKafka validateAiScraperKafka = new ValidateAiScraperKafka();
        validateAiScraperKafka.start();

    }

    private void start() {
        System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO Start");
        startExecute();
        System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO End");
    }

    private void startExecute() {
//        validateData();
        contrastData();
    }

    private void contrastData() {
        contrastTopicList.forEach(item -> {
            System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO Start topic: " + item);
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "173.236.57.146:9093,65.60.14.78:9093,65.60.14.134:9093");
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "ai_scraper_consumer_group_prod_bak");
            props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "2000");
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
            props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=se0Clarity99 password=LmNoPq123rStUv456WxYzAbC789dEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpOSOFJI0k;");
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            try (Consumer<String, String> consumer = new KafkaConsumer<>(props)) {
                consumer.subscribe(Collections.singletonList(item));
                int number = 0;
                int total = 100;
                int count = 0;
                while (true) {
                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(2000));
                    if (records.count() == 0) {
                        if (number++ > total) {
                            break;
                        }
                        continue;
                    }
                    count += records.count();
                    try {
                        records.forEach(record -> {
                            JSONObject jsonObject = JSONObject.parseObject(record.value());
                            if (jsonObject.getInteger("id") != null && jsonObject.getInteger("sendToQDate") != null) {
                                Integer id = jsonObject.getInteger("id");
                                Integer sendToQDate = jsonObject.getInteger("sendToQDate");
                                String prevCitations = jsonObject.getString("prev_citations");
                                if (prevCitations.contains("YosemitePark.com")) {
                                    LocalDateTime localDateTime = Instant.ofEpochMilli(record.timestamp()).atZone(ZoneId.systemDefault()).toLocalDateTime();
                                    System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO id: " + id +
                                            ", sendToQDate: " + sendToQDate + ", partition: " + record.partition() +
                                            ", offset: " + record.offset() + ", timestamp: " + localDateTime.format(DATE_MILLI_FORMATTER));
                                }
//                                if (ukIdList.contains(id)) {
//                                    LocalDateTime localDateTime = Instant.ofEpochMilli(record.timestamp()).atZone(ZoneId.systemDefault()).toLocalDateTime();
//                                    System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO id: " + id +
//                                            ", sendToQDate: " + sendToQDate + ", partition: " + record.partition() +
//                                            ", offset: " + record.offset() + ", timestamp: " + localDateTime.format(DATE_MILLI_FORMATTER));
//                                    System.out.println(record.value());
//                                }
                            }
                        });
                    } catch (Exception e) {
                        System.err.printf(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " ERROR JSON parse error. Exception: %s%n", e.getMessage());
                        e.printStackTrace();
                    }
//                    consumer.commitSync();
                }
                System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO topic: " + item + ", count: " + count);
            }
            System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO End topic: " + item);
        });
    }

    private void validateData() {
        topicList.forEach(item -> {
            System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO Start topic: " + item);
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "173.236.57.146:9093,65.60.14.78:9093,65.60.14.134:9093");
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "ai_scraper_consumer_group_prod_bak");
            props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "2000");
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
            props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=se0Clarity99 password=LmNoPq123rStUv456WxYzAbC789dEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpOSOFJI0k;");
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            try (Consumer<String, String> consumer = new KafkaConsumer<>(props)) {
                consumer.subscribe(Collections.singletonList(item));
                int number = 0;
                int total = 100;
                int count = 0;
                while (true) {
                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(2000));
                    if (records.count() == 0) {
                        if (number++ > total) {
                            break;
                        }
                        continue;
                    }
                    count += records.count();
                    try {
                        records.forEach(record -> {
                            JSONObject jsonObject = JSONObject.parseObject(record.value());
                            if (jsonObject.getInteger("keywordId") != null && jsonObject.getInteger("sendToQDate") != null) {
                                Integer keywordId = jsonObject.getInteger("keywordId");
                                Integer sendToQDate = jsonObject.getInteger("sendToQDate");
                                if (keywordIdList.contains(keywordId)) {
                                    LocalDateTime localDateTime = Instant.ofEpochMilli(record.timestamp()).atZone(ZoneId.systemDefault()).toLocalDateTime();
                                    System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO keywordId: " + keywordId +
                                            ", sendToQDate: " + sendToQDate + ", partition: " + record.partition() +
                                            ", offset: " + record.offset() + ", timestamp: " + localDateTime.format(DATE_MILLI_FORMATTER));
                                }
                            }
                        });
                    } catch (Exception e) {
                        System.err.printf(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " ERROR JSON parse error. Exception: %s%n", e.getMessage());
                        e.printStackTrace();
                    }
                }
                System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO topic: " + item + ", count: " + count);
            }
            System.out.println(LocalDateTime.now().format(DATE_MILLI_FORMATTER) + " INFO End topic: " + item);
        });
    }

}
