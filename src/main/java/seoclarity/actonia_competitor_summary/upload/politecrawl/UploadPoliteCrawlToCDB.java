package seoclarity.actonia_competitor_summary.upload.politecrawl;

import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.HtmlClickHouseEntity;
import seoclarity.actonia_competitor_summary.kafka.clientconstructor.consumer.UrlInspectionConsumerConstructor;
import seoclarity.actonia_competitor_summary.multithread.core.thread.threadpool.ThreadPoolManager;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2025/5/21 17:18
 */
public class UploadPoliteCrawlToCDB {

    private final Logger log = LogManager.getLogger(UploadPoliteCrawlToCDB.class.getName());

    public int threadCount;
    public int batchSize;
    private static final String TOPIC_POLITE_CRAWL = "polite_crawl";
    public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
    public static LinkedBlockingQueue<HtmlClickHouseEntity> htmlClickHouseEntityLinkedBlockingQueue = new LinkedBlockingQueue<>();

    public static void main(String[] args) {

        UploadPoliteCrawlToCDB uploadPoliteCrawlToCDB = new UploadPoliteCrawlToCDB();
        uploadPoliteCrawlToCDB.init(args);
        uploadPoliteCrawlToCDB.start();

    }

    private void init(String[] args) {
        log.info("Init Params");
        try {
            if (args.length == 0) {
                threadCount = 1;
                batchSize = 1000;
            } else if (args.length == 1) {
                threadCount = Integer.parseInt(args[0]);
                batchSize = 1000 * threadCount;
            } else {
                threadCount = Integer.parseInt(args[0]);
                batchSize = Integer.parseInt(args[1]) * threadCount;
            }
        } catch (Exception e) {
            log.error("Params error. Exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void start() {
        log.info("Start");
        log.info("Init threadPool");
        threadPool.init();
        log.info("Start execute");
        startExecute();
        log.info("Close threadPool");
        threadPool.destroy();
        log.info("End");
    }

    private void startExecute() {
        try (Consumer<String, String> consumer = UrlInspectionConsumerConstructor.getInstance().buildKafkaConsumerWithTopics(Collections.singleton(TOPIC_POLITE_CRAWL))) {
            List<String> resultList = new ArrayList<>();
            long count = 0L;
            int number = 0;
            int total = 100;
            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(500));
                if (records.count() == 0) {
                    if (number++ > total) {
                        break;
                    }
                    continue;
                }
                count += records.count();
                records.forEach(record -> resultList.add(record.value()));
                if (resultList.size() >= batchSize) {
                    listToDB(resultList);
                    consumer.commitSync();
                    resultList.clear();
                }
            }
            if (!resultList.isEmpty()) {
                listToDB(resultList);
                consumer.commitSync();
                resultList.clear();
            }
            log.info("Total count message: " + count);
        } catch (Exception e) {
            log.error("Kafka connection timed out. Exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void listToDB(List<String> resultList) {
        int partSize = (int) Math.ceil((double) resultList.size() / threadCount);
        List<List<String>> spiltResultList = IntStream.range(0, threadCount)
                .mapToObj(i -> resultList.stream().skip((long) i * partSize).limit(partSize)
                        .collect(Collectors.toList())).collect(Collectors.toList());
        int i = 0;
        for (List<String> item : spiltResultList) {
            while (true) {
                try {
                    PoliteCrawlCommand politeCrawlCommand = new PoliteCrawlCommand(true, ++i, item);
                    threadPool.execute(politeCrawlCommand);
                    break;
                } catch (Exception e) {
                    log.error("Thread: " + i + " Call exception. Exception: " + e.getMessage());
                }
            }
        }
        do {
            try {
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("Sleep exception. Exception: " + e.getMessage());
                e.printStackTrace();
            }
        } while (threadPool.getThreadPool().getActiveCount() > 0);
    }

}
