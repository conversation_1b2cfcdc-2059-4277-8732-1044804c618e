package seoclarity.actonia_competitor_summary.upload.politecrawl;

import java.util.Arrays;

public class PageAnalysisFragments implements Cloneable {

	private int rule;
	private String[] fragments;

	public int getRule() {
		return rule;
	}

	public void setRule(int rule) {
		this.rule = rule;
	}

	public String[] getFragments() {
		return fragments;
	}

	public void setFragments(String[] fragments) {
		this.fragments = fragments;
	}

	@Override
	public String toString() {
		return "PageAnalysisFragments [rule=" + rule + ", fragments=" + Arrays.toString(fragments) + "]";
	}

}
