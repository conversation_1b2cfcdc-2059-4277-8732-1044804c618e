package seoclarity.actonia_competitor_summary.upload.politecrawl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.HtmlClickHouseEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * Combined class that handles both field extraction from HtmlClickHouseEntity 
 * and mapping field names to their corresponding change IDs from ChangeIndicatorEnum.
 * This class combines the functionality of the original FieldExtractor and FieldToChangeIdMapper classes.
 */
public class FieldExtractorAndMapper {

    private static final Logger log = LogManager.getLogger(FieldExtractorAndMapper.class);

    // Field extractors for HtmlClickHouseEntity
    private final Map<String, Function<HtmlClickHouseEntity, String>> currentExtractors;
    
    // Field name to change ID mapping
    private final Map<String, Integer> fieldToChangeIdMap;

    /**
     * Constructor that initializes both field extractors and field to change ID mappings
     */
    public FieldExtractorAndMapper() {
        this.currentExtractors = initializeCurrentExtractors();
        this.fieldToChangeIdMap = initializeFieldMappings();
    }

    /**
     * Extracts the current value of a field from an HtmlClickHouseEntity
     * 
     * @param entity HtmlClickHouseEntity to extract field value from
     * @param fieldName The field name to extract
     * @return The extracted field value as a string, or null if extraction fails
     */
    public String getCurrentValue(HtmlClickHouseEntity entity, String fieldName) {
        Function<HtmlClickHouseEntity, String> extractor = currentExtractors.get(fieldName);
        if (extractor != null) {
            try {
                return extractor.apply(entity);
            } catch (Exception e) {
                log.warn("Error extracting field '{}' from current entity: {}", fieldName, e.getMessage());
                return null;
            }
        }
        log.warn("No extractor found for current field: {}", fieldName);
        return null;
    }

    /**
     * Get change ID for a given field name
     * 
     * @param fieldName The field name
     * @return The corresponding change ID, or null if not found
     */
    public Integer getChangeIdForField(String fieldName) {
        return fieldToChangeIdMap.get(fieldName);
    }

    /**
     * Check if a field name has a mapping to a change ID
     * 
     * @param fieldName The field name to check
     * @return true if mapping exists, false otherwise
     */
    public boolean hasMapping(String fieldName) {
        return fieldToChangeIdMap.containsKey(fieldName);
    }

    /**
     * Get all available field to change ID mappings
     * 
     * @return Map of field names to change IDs
     */
    public Map<String, Integer> getAllMappings() {
        return new HashMap<>(fieldToChangeIdMap);
    }

    /**
     * Initialize field extractors for HtmlClickHouseEntity
     * 
     * @return Map of field names to extractor functions
     */
    private Map<String, Function<HtmlClickHouseEntity, String>> initializeCurrentExtractors() {
        Map<String, Function<HtmlClickHouseEntity, String>> extractors = new HashMap<>();

        extractors.put("url", HtmlClickHouseEntity::getUrl);
        extractors.put("title", HtmlClickHouseEntity::getTitle);
        extractors.put("description", HtmlClickHouseEntity::getDescription);
        extractors.put("contentType", HtmlClickHouseEntity::getContent_type);
        extractors.put("content_type", HtmlClickHouseEntity::getContent_type);
        extractors.put("followFlg", entity -> Optional.ofNullable(entity.getFollow_flg()).map(String::valueOf).orElse(null));
        extractors.put("follow_flg", entity -> Optional.ofNullable(entity.getFollow_flg()).map(String::valueOf).orElse(null));
        extractors.put("canonical", HtmlClickHouseEntity::getCanonical);
        extractors.put("amphtmlHref", HtmlClickHouseEntity::getAmphtml_href);
        extractors.put("amphtml_href", HtmlClickHouseEntity::getAmphtml_href);
        extractors.put("analyzedUrlS", HtmlClickHouseEntity::getAnalyzed_url_s);
        extractors.put("analyzed_url_s", HtmlClickHouseEntity::getAnalyzed_url_s);
        extractors.put("archiveFlg", entity -> Optional.ofNullable(entity.getArchive_flg()).map(String::valueOf).orElse(null));
        extractors.put("archive_flg", entity -> Optional.ofNullable(entity.getArchive_flg()).map(String::valueOf).orElse(null));
        extractors.put("baseTag", HtmlClickHouseEntity::getBase_tag);
        extractors.put("base_tag", HtmlClickHouseEntity::getBase_tag);
        extractors.put("baseTagTarget", HtmlClickHouseEntity::getBase_tag_target);
        extractors.put("base_tag_target", HtmlClickHouseEntity::getBase_tag_target);
        extractors.put("viewportContent", HtmlClickHouseEntity::getViewport_content);
        extractors.put("viewport_content", HtmlClickHouseEntity::getViewport_content);
        extractors.put("responseCode", entity -> Integer.toString(entity.getResponse_code()));
        extractors.put("response_code", entity -> Integer.toString(entity.getResponse_code()));

        extractors.put("h1", HtmlClickHouseEntity::getH1);
        extractors.put("h1_array", HtmlClickHouseEntity::getH1);
        extractors.put("h2", HtmlClickHouseEntity::getH2);
        extractors.put("h2_array", HtmlClickHouseEntity::getH2);
        extractors.put("customData", HtmlClickHouseEntity::getCustom_data);
        extractors.put("custom_data", HtmlClickHouseEntity::getCustom_data);

        extractors.put("amphtmlFlag", entity -> Optional.ofNullable(entity.getAmphtml_flag()).map(String::valueOf).orElse(null));
        extractors.put("amphtml_flag", entity -> Optional.ofNullable(entity.getAmphtml_flag()).map(String::valueOf).orElse(null));
        extractors.put("baseTagFlag", entity -> Optional.ofNullable(entity.getBase_tag_flag()).map(String::valueOf).orElse(null));
        extractors.put("base_tag_flag", entity -> Optional.ofNullable(entity.getBase_tag_flag()).map(String::valueOf).orElse(null));
        extractors.put("indexable", entity -> Optional.ofNullable(entity.getIndexable()).map(String::valueOf).orElse(null));
        extractors.put("noodp", entity -> Optional.ofNullable(entity.getNoodp()).map(String::valueOf).orElse(null));
        extractors.put("nosnippet", entity -> Optional.ofNullable(entity.getNosnippet()).map(String::valueOf).orElse(null));
        extractors.put("noydir", entity -> Optional.ofNullable(entity.getNoydir()).map(String::valueOf).orElse(null));

        extractors.put("descriptionLength", entity -> Optional.ofNullable(entity.getDescription_length()).map(String::valueOf).orElse(null));
        extractors.put("description_length", entity -> Optional.ofNullable(entity.getDescription_length()).map(String::valueOf).orElse(null));
        extractors.put("h1Count", entity -> Optional.ofNullable(entity.getH1_count()).map(String::valueOf).orElse(null));
        extractors.put("h1_count", entity -> Optional.ofNullable(entity.getH1_count()).map(String::valueOf).orElse(null));
        extractors.put("h1Length", entity -> Optional.ofNullable(entity.getH1_length()).map(String::valueOf).orElse(null));
        extractors.put("h1_length", entity -> Optional.ofNullable(entity.getH1_length()).map(String::valueOf).orElse(null));
        extractors.put("outlinkCount", entity -> Optional.ofNullable(entity.getOutlink_count()).map(String::valueOf).orElse(null));
        extractors.put("outlink_count", entity -> Optional.ofNullable(entity.getOutlink_count()).map(String::valueOf).orElse(null));
        extractors.put("titleLength", entity -> Optional.ofNullable(entity.getTitle_length()).map(String::valueOf).orElse(null));
        extractors.put("title_length", entity -> Optional.ofNullable(entity.getTitle_length()).map(String::valueOf).orElse(null));
        extractors.put("redirectTimes", entity -> Optional.ofNullable(entity.getRedirect_times()).map(String::valueOf).orElse(null));
        extractors.put("redirect_times", entity -> Optional.ofNullable(entity.getRedirect_times()).map(String::valueOf).orElse(null));

        extractors.put("canonicalType", HtmlClickHouseEntity::getCanonical_type);
        extractors.put("canonical_type", HtmlClickHouseEntity::getCanonical_type);
        extractors.put("canonicalHeaderType", HtmlClickHouseEntity::getCanonical_header_type);
        extractors.put("canonical_header_type", HtmlClickHouseEntity::getCanonical_header_type);
        extractors.put("redirectFinalUrl", HtmlClickHouseEntity::getRedirect_final_url);
        extractors.put("redirect_final_url", HtmlClickHouseEntity::getRedirect_final_url);
        extractors.put("blockedByRobots", HtmlClickHouseEntity::getBlocked_by_robots);
        extractors.put("blocked_by_robots", HtmlClickHouseEntity::getBlocked_by_robots);

        extractors.put("canonicalFlg", entity -> Optional.ofNullable(entity.getCanonical_flg()).map(String::valueOf).orElse(null));
        extractors.put("canonical_flg", entity -> Optional.ofNullable(entity.getCanonical_flg()).map(String::valueOf).orElse(null));
        extractors.put("descriptionFlg", entity -> Optional.ofNullable(entity.getDescription_flg()).map(String::valueOf).orElse(null));
        extractors.put("description_flg", entity -> Optional.ofNullable(entity.getDescription_flg()).map(String::valueOf).orElse(null));
        extractors.put("h1Flg", entity -> Optional.ofNullable(entity.getH1_flg()).map(String::valueOf).orElse(null));
        extractors.put("h1_flg", entity -> Optional.ofNullable(entity.getH1_flg()).map(String::valueOf).orElse(null));
        extractors.put("indexFlg", entity -> Optional.ofNullable(entity.getIndex_flg()).map(String::valueOf).orElse(null));
        extractors.put("index_flg", entity -> Optional.ofNullable(entity.getIndex_flg()).map(String::valueOf).orElse(null));
        extractors.put("titleFlg", entity -> Optional.ofNullable(entity.getTitle_flg()).map(String::valueOf).orElse(null));
        extractors.put("title_flg", entity -> Optional.ofNullable(entity.getTitle_flg()).map(String::valueOf).orElse(null));
        extractors.put("canonicalHeaderFlag", entity -> Optional.ofNullable(entity.getCanonical_header_flag()).map(String::valueOf).orElse(null));
        extractors.put("canonical_header_flag", entity -> Optional.ofNullable(entity.getCanonical_header_flag()).map(String::valueOf).orElse(null));

        log.info("Initialized field extractors with {} entries", extractors.size());
        return extractors;
    }

    /**
     * Initialize field name to change ID mappings based on ChangeIndicatorEnum
     * 
     * @return Map of field names to change IDs
     */
    private Map<String, Integer> initializeFieldMappings() {
        Map<String, Integer> mappings = new HashMap<>();
        
        // Direct field mappings from ChangeIndicatorEnum
        mappings.put("alternate_links", ChangeIndicatorEnum.ALTERNATE_LINKS_CHG_IND.getId());
        mappings.put("amphtml_href", ChangeIndicatorEnum.AMPHTML_HREF_CHG_IND.getId());
        mappings.put("analyzed_url_s", ChangeIndicatorEnum.ANALYZED_URL_S_CHG_IND.getId());
        mappings.put("archive_flg", ChangeIndicatorEnum.ARCHIVE_FLG_CHG_IND.getId());
        mappings.put("base_tag", ChangeIndicatorEnum.BASE_TAG_CHG_IND.getId());
        mappings.put("base_tag_target", ChangeIndicatorEnum.BASE_TAG_TARGET_CHG_IND.getId());
        mappings.put("blocked_by_robots", ChangeIndicatorEnum.BLOCKED_BY_ROBOTS_CHG_IND.getId());
        mappings.put("canonical", ChangeIndicatorEnum.CANONICAL_CHG_IND.getId());
        mappings.put("canonical_header_flag", ChangeIndicatorEnum.CANONICAL_HEADER_FLAG_CHG_IND.getId());
        mappings.put("canonical_header_type", ChangeIndicatorEnum.CANONICAL_HEADER_TYPE_CHG_IND.getId());
        mappings.put("canonical_type", ChangeIndicatorEnum.CANONICAL_TYPE_CHG_IND.getId());
        mappings.put("canonical_url_is_consistent", ChangeIndicatorEnum.CANONICAL_URL_IS_CONSISTENT_CHG_IND.getId());
        mappings.put("content_type", ChangeIndicatorEnum.CONTENT_TYPE_CHG_IND.getId());
        mappings.put("custom_data", ChangeIndicatorEnum.CUSTOM_DATA_CHG_IND.getId());
        mappings.put("description", ChangeIndicatorEnum.DESCRIPTION_CHG_IND.getId());
        mappings.put("error_message", ChangeIndicatorEnum.ERROR_MESSAGE_CHG_IND.getId());
        mappings.put("follow_flg", ChangeIndicatorEnum.FOLLOW_FLG_CHG_IND.getId());
        mappings.put("h1", ChangeIndicatorEnum.H1_CHG_IND.getId());
        mappings.put("h2", ChangeIndicatorEnum.H2_CHG_IND.getId());
        mappings.put("header_noarchive", ChangeIndicatorEnum.HEADER_NOARCHIVE_CHG_IND.getId());
        mappings.put("header_nofollow", ChangeIndicatorEnum.HEADER_NOFOLLOW_CHG_IND.getId());
        mappings.put("header_noindex", ChangeIndicatorEnum.HEADER_NOINDEX_CHG_IND.getId());
        mappings.put("header_noodp", ChangeIndicatorEnum.HEADER_NOODP_CHG_IND.getId());
        mappings.put("header_nosnippet", ChangeIndicatorEnum.HEADER_NOSNIPPET_CHG_IND.getId());
        mappings.put("header_noydir", ChangeIndicatorEnum.HEADER_NOYDIR_CHG_IND.getId());
        mappings.put("hreflang_url_count", ChangeIndicatorEnum.HREFLANG_URL_COUNT_CHG_IND.getId());
        mappings.put("hreflang_links", ChangeIndicatorEnum.HREFLANG_LINKS_CHG_IND.getId());
        mappings.put("hreflang_links_out_count", ChangeIndicatorEnum.HREFLANG_LINKS_OUT_COUNT_CHG_IND.getId());
        mappings.put("indexable", ChangeIndicatorEnum.INDEXABLE_CHG_IND.getId());
        mappings.put("index_flg", ChangeIndicatorEnum.INDEX_FLG_CHG_IND.getId());
        mappings.put("insecure_resources", ChangeIndicatorEnum.INSECURE_RESOURCES_CHG_IND.getId());
        mappings.put("meta_charset", ChangeIndicatorEnum.META_CHARSET_CHG_IND.getId());
        mappings.put("meta_content_type", ChangeIndicatorEnum.META_CONTENT_TYPE_CHG_IND.getId());
        mappings.put("meta_disabled_sitelinks", ChangeIndicatorEnum.META_DISABLED_SITELINKS_CHG_IND.getId());
        mappings.put("meta_noodp", ChangeIndicatorEnum.META_NOODP_CHG_IND.getId());
        mappings.put("meta_nosnippet", ChangeIndicatorEnum.META_NOSNIPPET_CHG_IND.getId());
        mappings.put("meta_noydir", ChangeIndicatorEnum.META_NOYDIR_CHG_IND.getId());
        mappings.put("meta_redirect", ChangeIndicatorEnum.META_REDIRECT_CHG_IND.getId());
        mappings.put("mobile_rel_alternate_url_is_consistent", ChangeIndicatorEnum.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND.getId());
        mappings.put("noodp", ChangeIndicatorEnum.NOODP_CHG_IND.getId());
        mappings.put("nosnippet", ChangeIndicatorEnum.NOSNIPPET_CHG_IND.getId());
        mappings.put("noydir", ChangeIndicatorEnum.NOYDIR_CHG_IND.getId());
        mappings.put("og_markup", ChangeIndicatorEnum.OG_MARKUP_CHG_IND.getId());
        mappings.put("og_markup_length", ChangeIndicatorEnum.OG_MARKUP_LENGTH_CHG_IND.getId());
        mappings.put("page_link", ChangeIndicatorEnum.PAGE_LINK_CHG_IND.getId());
        mappings.put("redirect_chain", ChangeIndicatorEnum.REDIRECT_CHAIN_CHG_IND.getId());
        mappings.put("redirect_final_url", ChangeIndicatorEnum.REDIRECT_FINAL_URL_CHG_IND.getId());
        mappings.put("redirect_times", ChangeIndicatorEnum.REDIRECT_TIMES_CHG_IND.getId());
        mappings.put("response_code", ChangeIndicatorEnum.RESPONSE_CODE_CHG_IND.getId());
        mappings.put("response_headers", ChangeIndicatorEnum.RESPONSE_HEADERS_ADDED_IND.getId()); // Could also be REMOVED
        mappings.put("robots_contents", ChangeIndicatorEnum.ROBOTS_CONTENTS_CHG_IND.getId());
        mappings.put("structured_data", ChangeIndicatorEnum.STRUCTURED_DATA_CHG_IND.getId());
        mappings.put("title", ChangeIndicatorEnum.TITLE_CHG_IND.getId());
        mappings.put("viewport_content", ChangeIndicatorEnum.VIEWPORT_CONTENT_CHG_IND.getId());
        
        // Additional common aliases - camelCase versions
        mappings.put("responseCode", ChangeIndicatorEnum.RESPONSE_CODE_CHG_IND.getId());
        mappings.put("contentType", ChangeIndicatorEnum.CONTENT_TYPE_CHG_IND.getId());
        mappings.put("followFlg", ChangeIndicatorEnum.FOLLOW_FLG_CHG_IND.getId());
        mappings.put("archiveFlg", ChangeIndicatorEnum.ARCHIVE_FLG_CHG_IND.getId());
        mappings.put("indexFlg", ChangeIndicatorEnum.INDEX_FLG_CHG_IND.getId());
        mappings.put("baseTag", ChangeIndicatorEnum.BASE_TAG_CHG_IND.getId());
        mappings.put("baseTagTarget", ChangeIndicatorEnum.BASE_TAG_TARGET_CHG_IND.getId());
        mappings.put("blockedByRobots", ChangeIndicatorEnum.BLOCKED_BY_ROBOTS_CHG_IND.getId());
        mappings.put("errorMessage", ChangeIndicatorEnum.ERROR_MESSAGE_CHG_IND.getId());
        mappings.put("customData", ChangeIndicatorEnum.CUSTOM_DATA_CHG_IND.getId());
        mappings.put("viewportContent", ChangeIndicatorEnum.VIEWPORT_CONTENT_CHG_IND.getId());
        mappings.put("robotsContents", ChangeIndicatorEnum.ROBOTS_CONTENTS_CHG_IND.getId());
        mappings.put("structuredData", ChangeIndicatorEnum.STRUCTURED_DATA_CHG_IND.getId());
        mappings.put("redirectChain", ChangeIndicatorEnum.REDIRECT_CHAIN_CHG_IND.getId());
        mappings.put("redirectFinalUrl", ChangeIndicatorEnum.REDIRECT_FINAL_URL_CHG_IND.getId());
        mappings.put("redirectTimes", ChangeIndicatorEnum.REDIRECT_TIMES_CHG_IND.getId());
        mappings.put("pageLink", ChangeIndicatorEnum.PAGE_LINK_CHG_IND.getId());
        mappings.put("ogMarkup", ChangeIndicatorEnum.OG_MARKUP_CHG_IND.getId());
        mappings.put("ogMarkupLength", ChangeIndicatorEnum.OG_MARKUP_LENGTH_CHG_IND.getId());
        mappings.put("alternateLinks", ChangeIndicatorEnum.ALTERNATE_LINKS_CHG_IND.getId());
        mappings.put("amphtmlHref", ChangeIndicatorEnum.AMPHTML_HREF_CHG_IND.getId());
        mappings.put("analyzedUrlS", ChangeIndicatorEnum.ANALYZED_URL_S_CHG_IND.getId());
        mappings.put("canonicalType", ChangeIndicatorEnum.CANONICAL_TYPE_CHG_IND.getId());
        mappings.put("canonicalHeaderFlag", ChangeIndicatorEnum.CANONICAL_HEADER_FLAG_CHG_IND.getId());
        mappings.put("canonicalHeaderType", ChangeIndicatorEnum.CANONICAL_HEADER_TYPE_CHG_IND.getId());
        mappings.put("canonicalUrlIsConsistent", ChangeIndicatorEnum.CANONICAL_URL_IS_CONSISTENT_CHG_IND.getId());
        mappings.put("hreflangUrlCount", ChangeIndicatorEnum.HREFLANG_URL_COUNT_CHG_IND.getId());
        mappings.put("hreflangLinks", ChangeIndicatorEnum.HREFLANG_LINKS_CHG_IND.getId());
        mappings.put("hreflangLinksOutCount", ChangeIndicatorEnum.HREFLANG_LINKS_OUT_COUNT_CHG_IND.getId());
        mappings.put("insecureResources", ChangeIndicatorEnum.INSECURE_RESOURCES_CHG_IND.getId());
        mappings.put("metaCharset", ChangeIndicatorEnum.META_CHARSET_CHG_IND.getId());
        mappings.put("metaContentType", ChangeIndicatorEnum.META_CONTENT_TYPE_CHG_IND.getId());
        mappings.put("metaDisabledSitelinks", ChangeIndicatorEnum.META_DISABLED_SITELINKS_CHG_IND.getId());
        mappings.put("metaNoodp", ChangeIndicatorEnum.META_NOODP_CHG_IND.getId());
        mappings.put("metaNosnippet", ChangeIndicatorEnum.META_NOSNIPPET_CHG_IND.getId());
        mappings.put("metaNoydir", ChangeIndicatorEnum.META_NOYDIR_CHG_IND.getId());
        mappings.put("metaRedirect", ChangeIndicatorEnum.META_REDIRECT_CHG_IND.getId());
        mappings.put("mobileRelAlternateUrlIsConsistent", ChangeIndicatorEnum.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND.getId());
        mappings.put("headerNoarchive", ChangeIndicatorEnum.HEADER_NOARCHIVE_CHG_IND.getId());
        mappings.put("headerNofollow", ChangeIndicatorEnum.HEADER_NOFOLLOW_CHG_IND.getId());
        mappings.put("headerNoindex", ChangeIndicatorEnum.HEADER_NOINDEX_CHG_IND.getId());
        mappings.put("headerNoodp", ChangeIndicatorEnum.HEADER_NOODP_CHG_IND.getId());
        mappings.put("headerNosnippet", ChangeIndicatorEnum.HEADER_NOSNIPPET_CHG_IND.getId());
        mappings.put("headerNoydir", ChangeIndicatorEnum.HEADER_NOYDIR_CHG_IND.getId());
        mappings.put("responseHeaders", ChangeIndicatorEnum.RESPONSE_HEADERS_ADDED_IND.getId());
        
        log.info("Initialized field to change ID mappings with {} entries", mappings.size());
        return mappings;
    }
}
