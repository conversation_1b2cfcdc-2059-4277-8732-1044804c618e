package seoclarity.actonia_competitor_summary.upload.politecrawl;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.HtmlClickHouseEntity;
import seoclarity.actonia_competitor_summary.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.actonia_competitor_summary.upload.pagecrawl.UploadRealtimePageToCDBFromKafka;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2025/5/22 10:41
 */
public class PoliteCrawlCommand extends BaseThreadCommand {

    private final Logger log = LogManager.getLogger(PoliteCrawlCommand.class.getName());
    private int threadNumber;
    private List<String> resultList;

    public PoliteCrawlCommand() {
    }

    public PoliteCrawlCommand(boolean status, int threadNumber, List<String> resultList) {
        super.setStatus(status);
        this.resultList = resultList;
        this.threadNumber = threadNumber;
    }

    /**
     * 1.先判断有没有 lastMds
     * 2.   没有  ->
     *      有   ->  对比md5 根据 domain_id/url_murmur_hash/crawl_date 查询 dis_url_metrics_latest，只存不同的，上一次和当前都存下来 dis_html_change
     * 3.直接存 dis_url_metrics/dis_url_metrics_latest/dis_url_change_list/dis_target_url_html_file_name
     * @throws Exception
     */
    @Override
    protected void execute() throws Exception {
        long start = System.currentTimeMillis();
        int totalCount = resultList.size();
        insertBath();
        long end = System.currentTimeMillis();
        System.out.println(LocalDateTime.now().format(UploadRealtimePageToCDBFromKafka.DATE_MILLI_FORMATTER) + " INFO Thread: " + threadNumber + " Cost time: " + (end - start) * 1.0 / 1000 + "s" + " ResultCount: " + totalCount);
    }

    private void insertBath() {
        try {
            if (resultList == null || resultList.isEmpty()) {
                return;
            }
            resultList.forEach(item -> {
                JSONObject jsonObject = null;
                try {
                    jsonObject = JSONObject.parseObject(item);
                } catch (Exception e) {
                    log.error("JSON parse error. Exception: " + e.getMessage());
                    log.error("value: " + item);
                    e.printStackTrace();
                }
                if (jsonObject == null) {
                    return;
                }
                String lastSumMd5 = jsonObject.getString("lastSumMd5");
                final String crawlerResponse = jsonObject.getString("crawlerResponse");
                if (StringUtils.isBlank(lastSumMd5)) {
                    final HtmlClickHouseEntity htmlClickHouseEntity = UploadPoliteCrawlToCDB.gson.fromJson(crawlerResponse, HtmlClickHouseEntity.class);
                    UploadPoliteCrawlToCDB.htmlClickHouseEntityLinkedBlockingQueue.add(htmlClickHouseEntity);
                    if (UploadPoliteCrawlToCDB.htmlClickHouseEntityLinkedBlockingQueue.size() > 1000) {
                        // batch insert
                        this.batchInsert();
                    }
                } else {
                    // TODO 查询不同
                    final JSONObject currMd5 = jsonObject.getJSONObject("currMd5");
                    final JSONObject lastMd5 = jsonObject.getJSONObject("lastMd5");
                    // find diff value for each key between lastMd5 and currMd5
                    currMd5.entrySet().stream().filter(entry -> !lastMd5.containsKey(entry.getKey()))
                                    .map(entry -> {
                                        final String key = entry.getKey();

                                    })

                }
            });
        } catch (Exception e) {
            System.err.printf(LocalDateTime.now().format(UploadRealtimePageToCDBFromKafka.DATE_MILLI_FORMATTER) + " ERROR Thread: " + threadNumber + "2db exception. Exception: %s%n", e.getMessage());
            e.printStackTrace();
        }
    }

    private void insertWithRetry(Runnable insertOperation) throws InterruptedException {
        while (true) {
            try {
                insertOperation.run();
                break;
            } catch (Exception e) {
                log.error("Thread: " + threadNumber + " 2db exception. Exception: " + e.getMessage());
                e.printStackTrace();
                Thread.sleep(5000);
            }
        }
    }

    @Override
    protected void undo() throws Exception {

    }
    
    private void batchInsert() {
        final List<HtmlClickHouseEntity> htmlClickHouseEntities = new ArrayList<>();
        final LinkedBlockingQueue<HtmlClickHouseEntity> htmlClickHouseEntityLinkedBlockingQueue = UploadPoliteCrawlToCDB.htmlClickHouseEntityLinkedBlockingQueue;
        for (int i = 0; i < 1000; i++) {
            final HtmlClickHouseEntity poll = htmlClickHouseEntityLinkedBlockingQueue.poll();
            if (poll != null) {
                htmlClickHouseEntities.add(poll);
            }
        }

    }
}
