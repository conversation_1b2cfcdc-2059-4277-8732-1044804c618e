package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class IndexFlgIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getIndexFlgPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getIndexFlgCurrent();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getIndexFlgChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.INDEX_FLG_CHG_IND.id);
		temp.setPrevValue(previous.getIndex_flg());
		temp.setCurrValue(current.getCrawlerResponse().getIndex_flg());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.INDEX_FLG_CHG_IND.getField());
		targetUrlChangeIndClickHouseEntity.setIndexFlgCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setIndexFlgPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
