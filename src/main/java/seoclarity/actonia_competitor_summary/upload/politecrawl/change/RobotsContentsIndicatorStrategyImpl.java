package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

public abstract class RobotsContentsIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getRobotsContentsPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getRobotsContentsCurrent();
	}


}
