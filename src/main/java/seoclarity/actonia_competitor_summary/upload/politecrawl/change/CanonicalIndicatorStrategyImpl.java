package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
public abstract class CanonicalIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getCanonicalPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getCanonicalCurrent();
	}

}
