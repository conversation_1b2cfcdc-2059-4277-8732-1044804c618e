package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.ResponseHeaders;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class ResponseHeadersRemovedIndicatorStrategyImpl extends ResponseHeadersIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getResponseHeadersRemovedInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.RESPONSE_HEADERS_REMOVED_IND.id);
		temp.setPrevValue(convert(previous.getResponse_headers()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getResponse_headers()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.RESPONSE_HEADERS_REMOVED_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setResponseHeadersCurrent(gson.fromJson(currValue, ResponseHeaders[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setResponseHeadersCurrent(gson.fromJson(currValue, ResponseHeaders[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
