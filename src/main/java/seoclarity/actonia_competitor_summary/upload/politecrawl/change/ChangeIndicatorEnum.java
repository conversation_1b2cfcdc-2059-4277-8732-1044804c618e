package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import lombok.Getter;

@Getter
public enum ChangeIndicatorEnum {
	ALTERNATE_LINKS_CHG_IND(1, "alternate_links", true, new AlternateLinksIndicatorStrategyImpl()),
	AMPHTML_HREF_CHG_IND(2, "amphtml_href", false, new AmphtmlHrefIndicatorStrategyImpl()),
	ANALYZED_URL_S_CHG_IND(3, "analyzed_url_s", false, new AnalyzedUrlSIndicatorStrategyImpl()),
	ARCHIVE_FLG_CHG_IND(4, "archive_flg", false, new ArchiveFlgIndicatorStrategyImpl()),
	BASE_TAG_ADDED_IND(5, "base_tag", false, new BaseTagAddedIndicatorStrategyImpl()),
	BASE_TAG_CHG_IND(6, "base_tag", false, new BaseTagChgIndicatorStrategyImpl()),
	BASE_TAG_REMOVED_IND(7, "base_tag", false, new BaseTagRemovedIndicatorStrategyImpl()),
	BASE_TAG_TARGET_CHG_IND(8, "base_tag_target", false, new BaseTagTargetIndicatorStrategyImpl()),
	BLOCKED_BY_ROBOTS_CHG_IND(9, "blocked_by_robots", false, new BlockedByRobotsIndicatorStrategyImpl()),
	CANONICAL_ADDED_IND(10, "canonical", false, new CanonicalAddedIndicatorStrategyImpl()),
	CANONICAL_CHG_IND(11, "canonical", false, new CanonicalChgIndicatorStrategyImpl()),
	CANONICAL_HEADER_FLAG_CHG_IND(12, "canonical_header_flag", false, new CanonicalHeaderFlagIndicatorStrategyImpl()),
	CANONICAL_HEADER_TYPE_CHG_IND(13, "canonical_header_type", false, new CanonicalHeaderTypeIndicatorStrategyImpl()),
	CANONICAL_REMOVED_IND(14, "canonical", false, new CanonicalRemovedIndicatorStrategyImpl()),
	CANONICAL_TYPE_CHG_IND(15, "canonical_type", false, new CanonicalTypeIndicatorStrategyImpl()),
	CANONICAL_URL_IS_CONSISTENT_CHG_IND(16, "canonical_url_is_consistent", false, new CanonicalUrlIsConsistentIndicatorStrategyImpl()),
	CONTENT_TYPE_CHG_IND(17, "content_type", false, new ContentTypeIndicatorStrategyImpl()),
	CUSTOM_DATA_ADDED_IND(18, "custom_data", true, new CustomDataAddedIndicatorStrategyImpl()),
	CUSTOM_DATA_CHG_IND(19, "custom_data", true, new CustomDataChgIndicatorStrategyImpl()),
	CUSTOM_DATA_REMOVED_IND(20, "custom_data", true, new CustomDataRemovedIndicatorStrategyImpl()),
	DESCRIPTION_ADDED_IND(21, "description", false, new DescriptionAddedIndicatorStrategyImpl()),
	DESCRIPTION_CHG_IND(22, "description", false, new DescriptionChgIndicatorStrategyImpl()),
	DESCRIPTION_LENGTH_CHG_IND(23, "description_length", false, new DescriptionLengthIndicatorStrategyImpl()),
	DESCRIPTION_REMOVED_IND(24, "description", false, new DescriptionRemovedIndicatorStrategyImpl()),
	ERROR_MESSAGE_CHG_IND(25, "error_message", false, new ErrorMessageIndicatorStrategyImpl()),
	FINAL_RESPONSE_CODE_CHG_IND(26, "final_response_code", false, new FinalResponseCodeIndicatorStrategyImpl()),
	FOLLOW_FLG_CHG_IND(27, "follow_flg", false, new FollowFlgIndicatorStrategyImpl()),
	H1_ADDED_IND(28, "h1", false, new H1AddedIndicatorStrategyImpl()),
	H1_CHG_IND(29, "h1", false, new H1ChgIndicatorStrategyImpl()),
	H1_COUNT_CHG_IND(30, "h1_count", false, new H1CountIndicatorStrategyImpl()),
	H1_LENGTH_CHG_IND(31, "h1_length", false, new H1LengthIndicatorStrategyImpl()),
	H1_REMOVED_IND(32, "h1", false, new H1RemovedIndicatorStrategyImpl()),
	H2_ADDED_IND(33, "h2", false, new H2AddedIndicatorStrategyImpl()),
	H2_CHG_IND(34, "h2", false, new H2ChgIndicatorStrategyImpl()),
	H2_REMOVED_IND(35, "h2", false, new H2RemovedIndicatorStrategyImpl()),
	HEADER_NOARCHIVE_CHG_IND(36, "header_noarchive", false, new HeaderNoarchiveIndicatorStrategyImpl()),
	HEADER_NOFOLLOW_CHG_IND(37, "header_nofollow", false, new HeaderNofollowIndicatorStrategyImpl()),
	HEADER_NOINDEX_CHG_IND(38, "header_noindex", false, new HeaderNoindexIndicatorStrategyImpl()),
	HEADER_NOODP_CHG_IND(39, "header_noodp", false, new HeaderNoodpIndicatorStrategyImpl()),
	HEADER_NOSNIPPET_CHG_IND(40, "header_nosnippet", false, new HeaderNosnippetIndicatorStrategyImpl()),
	HEADER_NOYDIR_CHG_IND(41, "header_noydir", false, new HeaderNoydirIndicatorStrategyImpl()),
	HREFLANG_ERRORS_CHG_IND(42, "hreflang_errors", false, new HreflangErrorsIndicatorStrategyImpl()),
	HREFLANG_LINKS_ADDED_IND(43, "hreflang_links", true, new HreflangLinksAddedIndicatorStrategyImpl()),
	HREFLANG_LINKS_CHG_IND(44, "hreflang_links", true, new HreflangLinksChgIndicatorStrategyImpl()),
	HREFLANG_LINKS_OUT_COUNT_CHG_IND(45, "hreflang_links_out_count", false, new HreflangLinksOutCountIndicatorStrategyImpl()),
	HREFLANG_LINKS_REMOVED_IND(46, "hreflang_links", true, new HreflangLinksRemovedIndicatorStrategyImpl()),
	HREFLANG_URL_COUNT_CHG_IND(47, "hreflang_url_count", false, new HreflangUrlCountIndicatorStrategyImpl()),
	INDEXABLE_CHG_IND(48, "indexable", false, new IndexableIndicatorStrategyImpl()),
	INDEX_FLG_CHG_IND(49, "index_flg", false, new IndexFlgIndicatorStrategyImpl()),
	INSECURE_RESOURCES_CHG_IND(50, "insecure_resources", true, new InsecureResourcesIndicatorStrategyImpl()),
	META_CHARSET_CHG_IND(51, "meta_charset", false, new MetaCharsetIndicatorStrategyImpl()),
	META_CONTENT_TYPE_CHG_IND(52, "meta_content_type", false, new MetaContentTypeIndicatorStrategyImpl()),
	META_DISABLED_SITELINKS_CHG_IND(53, "meta_disabled_sitelinks", false, new MetaDisabledSitelinksIndicatorStrategyImpl()),
	META_NOODP_CHG_IND(54, "meta_noodp", false, new MetaNoodpIndicatorStrategyImpl()),
	META_NOSNIPPET_CHG_IND(55, "meta_nosnippet", false, new MetaNosnippetIndicatorStrategyImpl()),
	META_NOYDIR_CHG_IND(56, "meta_noydir", false, new MetaNoydirIndicatorStrategyImpl()),
	META_REDIRECT_CHG_IND(57, "meta_redirect", false, new MetaRedirectIndicatorStrategyImpl()),
	MIXED_REDIRECTS_CHG_IND(58, "mixed_redirects", false, new MixedRedirectsIndicatorStrategyImpl()),
	MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND(59, "mobile_rel_alternate_url_is_consistent", false, new MobileRelAlternateUrlIsConsistentIndicatorStrategyImpl()),
	NOODP_CHG_IND(60, "noodp", false, new NoodpIndicatorStrategyImpl()),
	NOSNIPPET_CHG_IND(61, "nosnippet", false, new NosnippetIndicatorStrategyImpl()),
	NOYDIR_CHG_IND(62, "noydir", false, new NoydirIndicatorStrategyImpl()),
	OG_MARKUP_CHG_IND(63, "og_markup", true, new OgMarkupChgIndicatorStrategyImpl()),
	OG_MARKUP_LENGTH_CHG_IND(64, "og_markup_length", false, new OgMarkupLengthIndicatorStrategyImpl()),
	OPEN_GRAPH_ADDED_IND(65, "open_graph", true, new OpenGraphAddedIndicatorStrategyImpl()),
	OPEN_GRAPH_REMOVED_IND(66, "open_graph", true, new OpenGraphRemovedIndicatorStrategyImpl()),
	OUTLINK_COUNT_CHG_IND(67, "outlink_count", false, new OutlinkCountIndicatorStrategyImpl()),
	PAGE_ANALYSIS_RESULTS_CHG_IND_JSON(68, "page_analysis_results_json", false, new PageAnalysisResultsChgIndJsonIndicatorStrategyImpl()),
	PAGE_LINK_CHG_IND(69, "page_link", true, new PageLinkIndicatorStrategyImpl()),
	REDIRECT_301_DETECTED_IND(70, "redirect_301", false, new Redirect301DetectedIndicatorStrategyImpl()),
	REDIRECT_301_REMOVED_IND(71, "redirect_301", false, new Redirect301RemovedIndicatorStrategyImpl()),
	REDIRECT_302_DETECTED_IND(72, "redirect_302", false, new Redirect302DetectedIndicatorStrategyImpl()),
	REDIRECT_302_REMOVED_IND(73, "redirect_302", false, new Redirect302RemovedIndicatorStrategyImpl()),
	REDIRECT_BLOCKED_CHG_IND(74, "redirect_blocked", false, new RedirectBlockedIndicatorStrategyImpl()),
	REDIRECT_BLOCKED_REASON_CHG_IND(75, "redirect_blocked_reason", false, new RedirectBlockedReasonIndicatorStrategyImpl()),
	REDIRECT_CHAIN_CHG_IND(76, "redirect_chain", true, new RedirectChainIndicatorStrategyImpl()),
	REDIRECT_DIFF_CODE_IND(77, "redirect_diff_code_ind", false, new RedirectDiffCodeIndicatorStrategyImpl()),
	REDIRECT_FINAL_URL_CHG_IND(78, "redirect_final_url", false, new RedirectFinalUrlIndicatorStrategyImpl()),
	REDIRECT_TIMES_CHG_IND(79, "redirect_times", false, new RedirectTimesIndicatorStrategyImpl()),
	RESPONSE_CODE_CHG_IND(80, "response_code", false, new ResponseCodeBaseIndicatorStrategyImpl()),
	RESPONSE_HEADERS_ADDED_IND(81, "response_headers", true, new ResponseHeadersAddedIndicatorStrategyImpl()),
	RESPONSE_HEADERS_REMOVED_IND(82, "response_headers", true, new ResponseHeadersRemovedIndicatorStrategyImpl()),
	ROBOTS_ADDED_IND(83, "robots", false, new RobotsAddedIndicatorStrategyImpl()),
	ROBOTS_CONTENTS_CHG_IND(84, "robots_contents", false, new RobotsContentsChgIndicatorStrategyImpl()),
	ROBOTS_REMOVED_IND(85, "robots", false, new RobotsRemovedIndicatorStrategyImpl()),
	ROBOTS_TXT_CHG_IND(86, "robots_txt", false, new RobotTxtIndicatorStrategyImpl()),
	STRUCTURED_DATA_CHG_IND(87, "structured_data", true, new StructuredDataIndicatorStrategyImpl()),
	TITLE_ADDED_IND(88, "title", false, new TitleAddedIndicatorStrategyImpl()),
	TITLE_CHG_IND(89, "title", false, new TitleChgIndicatorStrategyImpl()),
	TITLE_LENGTH_CHG_IND(90, "title_length", false, new TitleLengthIndicatorStrategyImpl()),
	TITLE_REMOVED_IND(91, "title", false, new TitleRemovedIndicatorStrategyImpl()),
	VIEWPORT_ADDED_IND(92, "viewport", false, new ViewportAddedIndicatorStrategyImpl()),
	VIEWPORT_CONTENT_CHG_IND(93, "viewport_content", false, new ViewportContentChgIndicatorStrategyImpl()),
	VIEWPORT_REMOVED_IND(94, "viewport", false, new ViewportRemovedIndicatorStrategyImpl()),
	STATUS_404_DETECTED_IND(103, "404", false, new Status404DetectedIndicatorStrategyImpl()),
	STATUS_404_REMOVED_IND(104, "404", false, new Status404RemovedIndicatorStrategyImpl());

	public final int id;
	private final String field;
	private final boolean bigDataFlg;
	private final IndicatorStrategy strategy;

	ChangeIndicatorEnum(int id, String field, boolean bigDataFlg, IndicatorStrategy strategy) {
		this.id = id;
		this.field = field;
		this.bigDataFlg = bigDataFlg;
		this.strategy = strategy;
	}

	public static ChangeIndicatorEnum fromId(int id) {
		for (ChangeIndicatorEnum e : values()) {
			if (e.getId() == id) {
				return e;
			}
		}
		throw new IllegalArgumentException("Invalid id: " + id);
	}

	public static ChangeIndicatorEnum fromIndicator(String indicator) {
		for (ChangeIndicatorEnum e : values()) {
			if (e.getField().equals(indicator)) {
				return e;
			}
		}
		throw new IllegalArgumentException("No matching indicator for: " + indicator);
	}
}
