package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.StructuredData;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public class StructuredDataIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getStructuredDataPrevious());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getStructuredDataCurrent());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convertStructuredData(changeIndClickHouseEntity.getStructuredDataPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return this.convertStructuredData(changeIndClickHouseEntity.getStructuredDataCurrent());
	}

	private String convertStructuredData(StructuredData structuredDataPrevious) {
		final Boolean previousStructuredDataValid = Optional.ofNullable(structuredDataPrevious)
				.map(StructuredData::getData)
				.map(data ->
						Optional.ofNullable(data.getCheck_structured_data()).map(arr -> arr.length > 0).orElse(false) ||
								Optional.ofNullable(data.getValidate_structured_data()).map(arr -> arr.length > 0).orElse(false))
				.orElse(false);
		if (!previousStructuredDataValid) return null;
		final String json = gson.toJson(structuredDataPrevious, StructuredData.class);
		if (!checkJson(json)) return null;
		return CrawlerUtils.getSortedCharactersHashCode(json);
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getStructuredDataChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.STRUCTURED_DATA_CHG_IND.id);
		temp.setPrevValue(temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.STRUCTURED_DATA));
		final String sortedCharactersHashCode = this.convert(current);
		temp.setCurrValue(sortedCharactersHashCode);
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.STRUCTURED_DATA_CHG_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setStructuredDataCurrent(gson.fromJson(currValue, StructuredData.class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setStructuredDataPrevious(gson.fromJson(currValue, StructuredData.class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

	private String convert(HtmlClickHouseEntity current) {
		final StructuredData structuredData = current.getCrawlerResponse().getStructured_data();
		if (structuredData != null) {
			final String json = gson.toJson(structuredData);
			if (checkJson(json)) {
				return CrawlerUtils.getSortedCharactersHashCode(json);
			}
		}
		return null;
	}
}
