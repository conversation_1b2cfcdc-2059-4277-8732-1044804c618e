package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.OgMarkup;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class OgMarkupChgIndicatorStrategyImpl extends OgMarkupIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getOgMarkupChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.OG_MARKUP_CHG_IND.id);
		temp.setPrevValue(temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.OG_MARKUP));
		temp.setCurrValue(this.convert(current.getCrawlerResponse().getOg_markup()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.OG_MARKUP_CHG_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setOgMarkupCurrent(gson.fromJson(currValue, OgMarkup[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setOgMarkupPrevious(gson.fromJson(currValue, OgMarkup[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
