package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

public abstract class ResponseCodeIndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getResponseCodePrevious();
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getResponseCodeCurrent();
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getResponseCodePrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getResponseCodeCurrent();
	}
}
