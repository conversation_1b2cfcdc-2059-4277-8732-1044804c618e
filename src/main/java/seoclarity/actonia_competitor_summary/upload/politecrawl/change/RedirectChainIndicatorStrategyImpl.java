package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.RedirectChain;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class RedirectChainIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getRedirectChainPrevious());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getRedirectChainCurrent());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getRedirectChainPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getRedirectChainCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectChainChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.REDIRECT_CHAIN_CHG_IND.id);
		temp.setPrevValue(temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.REDIRECT_CHAIN));
		temp.setCurrValue(convert(current.getCrawlerResponse().getRedirect_chain()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.REDIRECT_CHAIN_CHG_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setRedirectChainCurrent(gson.fromJson(currValue, RedirectChain[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setRedirectChainPrevious(gson.fromJson(currValue, RedirectChain[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
