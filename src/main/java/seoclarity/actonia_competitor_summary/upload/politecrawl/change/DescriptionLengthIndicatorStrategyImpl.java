package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class DescriptionLengthIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getDescriptionLengthPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getDescriptionLengthCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getDescriptionLengthChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.DESCRIPTION_LENGTH_CHG_IND.id);
		temp.setPrevValue(convert(previous.getDescription_length()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getDescription_length()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.DESCRIPTION_LENGTH_CHG_IND.getField());
		final int currValue = Integer.parseInt(htmlChange.getCurrValue());
		if (currValue != -1) {
			targetUrlChangeIndClickHouseEntity.setDescriptionLengthCurrent(currValue);
		}
		final int prevValue = Integer.parseInt(htmlChange.getPrevValue());
		if (prevValue != -1) {
			targetUrlChangeIndClickHouseEntity.setDescriptionLengthPrevious(prevValue);
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
