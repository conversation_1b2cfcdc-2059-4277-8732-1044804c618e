package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;

public class AmphtmlHrefIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getAmphtmlHrefPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getAmphtmlHrefCurrent();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getAmphtmlHrefChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.AMPHTML_HREF_CHG_IND.id);
		temp.setPrevValue(previous.getAmphtml_href());
		temp.setCurrValue(current.getCrawlerResponse().getAmphtml_href());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.AMPHTML_HREF_CHG_IND.getField());
		targetUrlChangeIndClickHouseEntity.setAmphtmlHrefCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setAmphtmlHrefPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
