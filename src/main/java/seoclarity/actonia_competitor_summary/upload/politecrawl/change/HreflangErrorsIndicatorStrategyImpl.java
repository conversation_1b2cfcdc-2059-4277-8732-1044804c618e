package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.HreflangErrors;
import com.google.gson.Gson;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class HreflangErrorsIndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getHreflangErrorsPrevious());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getHreflangErrorsCurrent());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return this.convert(changeIndClickHouseEntity.getHreflangErrorsPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return this.convert(changeIndClickHouseEntity.getHreflangErrorsCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangErrorsChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.HREFLANG_ERRORS_CHG_IND.id);
		temp.setPrevValue(temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.HREFLANG_ERRORS));
		temp.setCurrValue(this.convert(current.getCrawlerResponse().getHreflang_errors()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.HREFLANG_ERRORS_CHG_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setHreflangErrorsCurrent(gson.fromJson(currValue, HreflangErrors.class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setHreflangErrorsPrevious(gson.fromJson(currValue, HreflangErrors.class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

	public String convert(HreflangErrors hreflangErrors) {
		String result = null;
		if (hreflangErrors != null) {
			final String json = new Gson().toJson(hreflangErrors);
			if (StringUtils.isNotBlank(json) && json.length() > 2) {
				result = CrawlerUtils.getSortedCharactersHashCode(json);
			}
		}
		return result;
	}

}
