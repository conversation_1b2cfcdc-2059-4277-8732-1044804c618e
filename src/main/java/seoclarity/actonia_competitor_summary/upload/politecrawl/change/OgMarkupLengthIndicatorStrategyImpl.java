package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class OgMarkupLengthIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getOgMarkupLengthPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getOgMarkupLengthCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getOgMarkupLengthChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.OG_MARKUP_LENGTH_CHG_IND.id);
		temp.setPrevValue(convert(previous.getOg_markup_length()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getOg_markup_length()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.OG_MARKUP_LENGTH_CHG_IND.getField());
		final int currValue = Integer.parseInt(htmlChange.getCurrValue());
		if (currValue != -1) {
			targetUrlChangeIndClickHouseEntity.setOgMarkupLengthCurrent(currValue);
		}
		final int prevValue = Integer.parseInt(htmlChange.getPrevValue());
		if (prevValue != -1) {
			targetUrlChangeIndClickHouseEntity.setOgMarkupLengthPrevious(prevValue);
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
