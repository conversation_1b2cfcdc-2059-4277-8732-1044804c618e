package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class H2ChgIndicatorStrategyImpl extends H2IndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getH2ChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.H2_CHG_IND.id);
		temp.setPrevValue(gson.toJson(previous.getH2_array()));
		temp.setCurrValue(gson.toJson(current.getCrawlerResponse().getH2()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.H2_CHG_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setH2Current(gson.fromJson(currValue, String[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setH2Previous(gson.fromJson(currValue, String[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}
}
