package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.*;
import com.actonia.utils.CrawlerUtils;
import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public interface IndicatorStrategy {

	Gson gson = new Gson();

	default String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convertPrevValue(changeIndClickHouseEntity);
	}

	default String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convertCurrValue(changeIndClickHouseEntity);
	}

	String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity);

	String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity);

	boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity);

	HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current);

    default TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		final TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
		targetUrlChangeIndClickHouseEntity.setUrl(htmlChange.getUrl());
		targetUrlChangeIndClickHouseEntity.setCurrentCrawlTimestamp(htmlChange.getCurrCrawlTimestamp());
		targetUrlChangeIndClickHouseEntity.setPreviousCrawlTimestamp(htmlChange.getPrevCrawlTimestamp());
		targetUrlChangeIndClickHouseEntity.setUrlHash(htmlChange.getUrl_hash());
		targetUrlChangeIndClickHouseEntity.setUrlMurmurHash(htmlChange.getUrl_murmur_hash());
		targetUrlChangeIndClickHouseEntity.setResponseCodeCurrent(htmlChange.getResponse_code_current());
		targetUrlChangeIndClickHouseEntity.setResponseCodePrevious(htmlChange.getResponse_code_previous());
		return targetUrlChangeIndClickHouseEntity;
    }

    default String convert(Boolean bool) {
		if (bool == null) return null;
		return bool ? "1" : "0";
	}

	default String convert(Integer integer) {
		if (integer == null || integer == -1) return null;
		return Integer.toString(integer, 10);
	}

	default String rawValue(Object[] objects) {
		if (objects == null || objects.length == 0) return null;
		return gson.toJson(objects);
	}

	default String rawValue(Object object) {
		if (object == null) return null;
		return gson.toJson(object);
	}

	default String convert(Object[] objects) {
		if (objects == null || objects.length == 0) return null;
		final String json = gson.toJson(objects);
		if (!checkJson(json)) return null;
		return CrawlerUtils.getSortedCharactersHashCode(json);
	}

	default String convert(Object object) {
		if (object == null) return null;
		final String json = gson.toJson(object);
		if (!checkJson(json)) return null;
		return CrawlerUtils.getMd5HashCode(json);
	}

	default Boolean convertBoolean(String string) {
		if (StringUtils.isNotBlank(string)) {
			final int i = Integer.parseInt(string);
			return i == -1 ? null : BooleanUtils.toBoolean(i);
		}
		return null;
	}

	default boolean checkJson(String json) {
		return StringUtils.isNotBlank(json) && json.length() > 2;
	}
}
