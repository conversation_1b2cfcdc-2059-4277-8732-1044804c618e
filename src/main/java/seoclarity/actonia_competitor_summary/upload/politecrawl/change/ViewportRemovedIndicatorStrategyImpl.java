package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;

public class ViewportRemovedIndicatorStrategyImpl extends ViewportContentIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getViewportRemovedInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.VIEWPORT_REMOVED_IND.id);
		temp.setPrevValue(previous.getViewportContent());
		temp.setCurrValue(current.getCrawlerResponse().getViewport_content());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.VIEWPORT_REMOVED_IND.getField());
		targetUrlChangeIndClickHouseEntity.setViewportContentCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setViewportContentPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
