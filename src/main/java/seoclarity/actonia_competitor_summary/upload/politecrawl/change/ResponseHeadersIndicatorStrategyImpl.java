package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

public abstract class ResponseHeadersIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getResponseHeadersPrevious());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getResponseHeadersCurrent());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getResponseHeadersPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getResponseHeadersCurrent());
	}
}
