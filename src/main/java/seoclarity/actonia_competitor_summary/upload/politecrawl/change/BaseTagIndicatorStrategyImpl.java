package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

public abstract class BaseTagIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getBaseTagPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getBaseTagCurrent());
	}
}
