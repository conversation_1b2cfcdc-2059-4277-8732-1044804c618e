package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class MobileRelAlternateUrlIsConsistentIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND.id);
		temp.setPrevValue(convert(previous.getMobile_rel_alternate_url_is_consistent()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND.getField());
		targetUrlChangeIndClickHouseEntity.setMobileRelAlternateUrlIsConsistentCurrent(convertBoolean(htmlChange.getCurrValue()));
		targetUrlChangeIndClickHouseEntity.setMobileRelAlternateUrlIsConsistentPrevious(convertBoolean(htmlChange.getPrevValue()));
		return targetUrlChangeIndClickHouseEntity;
	}

}
