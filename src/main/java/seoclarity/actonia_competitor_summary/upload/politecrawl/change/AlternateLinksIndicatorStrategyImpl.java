package seoclarity.actonia_competitor_summary.upload.politecrawl.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.AlternateLinks;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class AlternateLinksIndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getAlternateLinksPrevious());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getAlternateLinksCurrent());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		final AlternateLinks[] alternateLinksPrevious = changeIndClickHouseEntity.getAlternateLinksPrevious();
		return convert(alternateLinksPrevious);
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		final AlternateLinks[] alternateLinksCurrent = changeIndClickHouseEntity.getAlternateLinksCurrent();
		return convert(alternateLinksCurrent);
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getAlternateLinksChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.ALTERNATE_LINKS_CHG_IND.id);
		final String prevValue = temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.ALTERNATE_LINKS);
		temp.setPrevValue(prevValue);
		final AlternateLinks[] alternateLinks = current.getCrawlerResponse().getAlternate_links();
		if (alternateLinks != null) {
			final String currValue = convert(alternateLinks);
			temp.setCurrValue(currValue);
		}
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.ALTERNATE_LINKS_CHG_IND.getField());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setAlternateLinksCurrent(gson.fromJson(currValue, AlternateLinks[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setAlternateLinksPrevious(gson.fromJson(prevValue, AlternateLinks[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}