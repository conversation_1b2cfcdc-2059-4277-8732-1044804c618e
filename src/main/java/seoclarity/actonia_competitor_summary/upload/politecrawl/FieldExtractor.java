package seoclarity.actonia_competitor_summary.upload.politecrawl;

import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.HtmlClickHouseEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

public class FieldExtractor {

    private static final Logger log = LogManager.getLogger(FieldExtractor.class);
    private static final Gson gson = new Gson();

    private final Map<String, Function<HtmlClickHouseEntity, String>> currentExtractors;

    public FieldExtractor() {
        this.currentExtractors = initializeCurrentExtractors();
    }

    public String getCurrentValue(HtmlClickHouseEntity entity, String fieldName) {
        Function<HtmlClickHouseEntity, String> extractor = currentExtractors.get(fieldName);
        if (extractor != null) {
            try {
                return extractor.apply(entity);
            } catch (Exception e) {
                log.warn("Error extracting field '{}' from current entity: {}", fieldName, e.getMessage());
                return null;
            }
        }
        log.warn("No extractor found for current field: {}", fieldName);
        return null;
    }

    private Map<String, Function<HtmlClickHouseEntity, String>> initializeCurrentExtractors() {
        Map<String, Function<HtmlClickHouseEntity, String>> extractors = new HashMap<>();

        extractors.put("url", entity -> entity.getUrl());
        extractors.put("title", entity -> entity.getTitle());
        extractors.put("description", entity -> entity.getDescription());
        extractors.put("contentType", entity -> entity.getContent_type());
        extractors.put("content_type", entity -> entity.getContent_type());
        extractors.put("followFlg", entity -> Optional.ofNullable(entity.getFollow_flg()).map(String::valueOf).orElse(null));
        extractors.put("follow_flg", entity -> Optional.ofNullable(entity.getFollow_flg()).map(String::valueOf).orElse(null));
        extractors.put("canonical", entity -> entity.getCanonical());
        extractors.put("amphtmlHref", entity -> entity.getAmphtml_href());
        extractors.put("amphtml_href", entity -> entity.getAmphtml_href());
        extractors.put("analyzedUrlS", entity -> entity.getAnalyzed_url_s());
        extractors.put("analyzed_url_s", entity -> entity.getAnalyzed_url_s());
        extractors.put("archiveFlg", entity -> Optional.ofNullable(entity.getArchive_flg()).map(String::valueOf).orElse(null));
        extractors.put("archive_flg", entity -> Optional.ofNullable(entity.getArchive_flg()).map(String::valueOf).orElse(null));
        extractors.put("baseTag", entity -> entity.getBase_tag());
        extractors.put("base_tag", entity -> entity.getBase_tag());
        extractors.put("baseTagTarget", entity -> entity.getBase_tag_target());
        extractors.put("base_tag_target", entity -> entity.getBase_tag_target());
        extractors.put("viewportContent", entity -> entity.getViewport_content());
        extractors.put("viewport_content", entity -> entity.getViewport_content());
        extractors.put("responseCode", entity -> Integer.toString(entity.getResponse_code()));
        extractors.put("response_code", entity -> Integer.toString(entity.getResponse_code()));

        extractors.put("h1", entity -> entity.getH1());
        extractors.put("h1_array", entity -> entity.getH1());
        extractors.put("h2", entity -> entity.getH2());
        extractors.put("h2_array", entity -> entity.getH2());
        extractors.put("customData", entity -> entity.getCustom_data());
        extractors.put("custom_data", entity -> entity.getCustom_data());

        extractors.put("amphtmlFlag", entity -> Optional.ofNullable(entity.getAmphtml_flag()).map(String::valueOf).orElse(null));
        extractors.put("amphtml_flag", entity -> Optional.ofNullable(entity.getAmphtml_flag()).map(String::valueOf).orElse(null));
        extractors.put("baseTagFlag", entity -> Optional.ofNullable(entity.getBase_tag_flag()).map(String::valueOf).orElse(null));
        extractors.put("base_tag_flag", entity -> Optional.ofNullable(entity.getBase_tag_flag()).map(String::valueOf).orElse(null));
        extractors.put("indexable", entity -> Optional.ofNullable(entity.getIndexable()).map(String::valueOf).orElse(null));
        extractors.put("noodp", entity -> Optional.ofNullable(entity.getNoodp()).map(String::valueOf).orElse(null));
        extractors.put("nosnippet", entity -> Optional.ofNullable(entity.getNosnippet()).map(String::valueOf).orElse(null));
        extractors.put("noydir", entity -> Optional.ofNullable(entity.getNoydir()).map(String::valueOf).orElse(null));

        extractors.put("descriptionLength", entity -> Optional.ofNullable(entity.getDescription_length()).map(String::valueOf).orElse(null));
        extractors.put("description_length", entity -> Optional.ofNullable(entity.getDescription_length()).map(String::valueOf).orElse(null));
        extractors.put("h1Count", entity -> Optional.ofNullable(entity.getH1_count()).map(String::valueOf).orElse(null));
        extractors.put("h1_count", entity -> Optional.ofNullable(entity.getH1_count()).map(String::valueOf).orElse(null));
        extractors.put("h1Length", entity -> Optional.ofNullable(entity.getH1_length()).map(String::valueOf).orElse(null));
        extractors.put("h1_length", entity -> Optional.ofNullable(entity.getH1_length()).map(String::valueOf).orElse(null));
        extractors.put("outlinkCount", entity -> Optional.ofNullable(entity.getOutlink_count()).map(String::valueOf).orElse(null));
        extractors.put("outlink_count", entity -> Optional.ofNullable(entity.getOutlink_count()).map(String::valueOf).orElse(null));
        extractors.put("titleLength", entity -> Optional.ofNullable(entity.getTitle_length()).map(String::valueOf).orElse(null));
        extractors.put("title_length", entity -> Optional.ofNullable(entity.getTitle_length()).map(String::valueOf).orElse(null));
        extractors.put("redirectTimes", entity -> Optional.ofNullable(entity.getRedirect_times()).map(String::valueOf).orElse(null));
        extractors.put("redirect_times", entity -> Optional.ofNullable(entity.getRedirect_times()).map(String::valueOf).orElse(null));

        extractors.put("canonicalType", entity -> entity.getCanonical_type());
        extractors.put("canonical_type", entity -> entity.getCanonical_type());
        extractors.put("canonicalHeaderType", entity -> entity.getCanonical_header_type());
        extractors.put("canonical_header_type", entity -> entity.getCanonical_header_type());
        extractors.put("redirectFinalUrl", entity -> entity.getRedirect_final_url());
        extractors.put("redirect_final_url", entity -> entity.getRedirect_final_url());
        extractors.put("blockedByRobots", entity -> entity.getBlocked_by_robots());
        extractors.put("blocked_by_robots", entity -> entity.getBlocked_by_robots());

        extractors.put("canonicalFlg", entity -> Optional.ofNullable(entity.getCanonical_flg()).map(String::valueOf).orElse(null));
        extractors.put("canonical_flg", entity -> Optional.ofNullable(entity.getCanonical_flg()).map(String::valueOf).orElse(null));
        extractors.put("descriptionFlg", entity -> Optional.ofNullable(entity.getDescription_flg()).map(String::valueOf).orElse(null));
        extractors.put("description_flg", entity -> Optional.ofNullable(entity.getDescription_flg()).map(String::valueOf).orElse(null));
        extractors.put("h1Flg", entity -> Optional.ofNullable(entity.getH1_flg()).map(String::valueOf).orElse(null));
        extractors.put("h1_flg", entity -> Optional.ofNullable(entity.getH1_flg()).map(String::valueOf).orElse(null));
        extractors.put("indexFlg", entity -> Optional.ofNullable(entity.getIndex_flg()).map(String::valueOf).orElse(null));
        extractors.put("index_flg", entity -> Optional.ofNullable(entity.getIndex_flg()).map(String::valueOf).orElse(null));
        extractors.put("titleFlg", entity -> Optional.ofNullable(entity.getTitle_flg()).map(String::valueOf).orElse(null));
        extractors.put("title_flg", entity -> Optional.ofNullable(entity.getTitle_flg()).map(String::valueOf).orElse(null));
        extractors.put("canonicalHeaderFlag", entity -> Optional.ofNullable(entity.getCanonical_header_flag()).map(String::valueOf).orElse(null));
        extractors.put("canonical_header_flag", entity -> Optional.ofNullable(entity.getCanonical_header_flag()).map(String::valueOf).orElse(null));

        return extractors;
    }
}
