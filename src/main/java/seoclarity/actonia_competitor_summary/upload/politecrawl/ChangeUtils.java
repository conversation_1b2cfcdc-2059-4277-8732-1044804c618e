package seoclarity.actonia_competitor_summary.upload.politecrawl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.HtmlChange;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.HtmlClickHouseEntity;
import seoclarity.actonia_competitor_summary.upload.politecrawl.change.ChangeIndicatorEnum;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class ChangeUtils {

    public static final String MULTI_STRING_SEPARATOR = "!_!";
    private static final Gson gson = new Gson();

    public String ALT_IMG_LIST = "alt_img_list";
    public String AMPHTML_FLAG = "amphtml_flag";
    public String AMPHTML_HREF = "amphtml_href";
    public String ANALYZED_URL_FLG_S = "analyzed_url_flg_s";
    public String ANALYZED_URL_S = "analyzed_url_s";

    public String ARCHIVE_FLG = "archive_flg";
    public String ARCHIVE_FLG_X_TAG = "archive_flg_x_tag";
    public String CONTENT_TYPE = "content_type";
    public String DESCRIPTION = "description";
    public String DESCRIPTION_FLG = "description_flg";
    public String DESCRIPTION_LENGTH = "description_length";
    public String DESCRIPTION_SIMHASH = "description_simhash";
    public String ERROR_MESSAGE = "error_message";
    public String FINAL_RESPONSE_CODE = "final_response_code";
    public String FOLDER_LEVEL_1 = "folder_level_1";
    public String FOLDER_LEVEL_2 = "folder_level_2";
    public String FOLDER_LEVEL_3 = "folder_level_3";
    public String FOLDER_LEVEL_COUNT = "folder_level_count";
    public String FOLLOW_FLG = "follow_flg";
    public String FOLLOW_FLG_X_TAG = "follow_flg_x_tag";
    public String H1_FLG = "h1_flg";
    public String H1_SIMHASH = "h1_simhash";
    public String H2_SIMHASH = "h2_simhash";
    public String HEADER_NOARCHIVE = "header_noarchive";
    public String HEADER_NOFOLLOW = "header_nofollow";
    public String HEADER_NOINDEX = "header_noindex";
    public String HEADER_NOODP = "header_noodp";
    public String HEADER_NOSNIPPET = "header_nosnippet";
    public String HEADER_NOYDIR = "header_noydir";
    public String HREFLANG_ERRORS = "hreflang_errors";
    public String INSECURE_RESOURCES = "insecure_resources";
    public String INSECURE_RESOURCES_FLAG = "insecure_resources_flag";
    public String LONG_REDIRECT = "long_redirect";
    public String META_CONTENT_TYPE = "meta_content_type";
    public String META_DISABLED_SITELINKS = "meta_disabled_sitelinks";
    public String META_NOODP = "meta_noodp";
    public String META_NOSNIPPET = "meta_nosnippet";
    public String META_NOYDIR = "meta_noydir";
    public String META_REDIRECT = "meta_redirect";
    public String MIXED_REDIRECTS = "mixed_redirects";
    public String MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT = "mobile_rel_alternate_url_is_consistent";
    public String NOODP = "noodp";
    public String NOSNIPPET = "nosnippet";
    public String NOYDIR = "noydir";
    public String REDIRECT_FLG = "redirect_flg";
    public String REDIRECT_TIMES = "redirect_times";
    public String REL_NEXT_HTML_URL = "rel_next_html_url";
    public String REL_NEXT_URL_IS_CONSISTENT = "rel_next_url_is_consistent";
    public String REL_PREV_URL_IS_CONSISTENT = "rel_prev_url_is_consistent";
    public String REQUEST_HEADERS = "request_headers";
    public String REQUEST_TIME = "request_time";
    public String RETRY_ATTEMPTED = "retry_attempted";
    public String ROBOTS = "robots";
    public String ROBOTS_FLG = "robots_flg";
    public String ROBOTS_FLG_X_TAG = "robots_flg_x_tag";
    public String SERVER_RESPONSE_TIME = "server_response_time";
    public String SOURCE_URL = "source_url";
    public String SPLASH_TOOK = "splash_took";
    public String TITLE_SIMHASH = "title_simhash";
    public String VIEWPORT_FLAG = "viewport_flag";
    public String BLOCKED_BY_ROBOTS = "blocked_by_robots";
    public String CANONICAL = "canonical";
    public String CANONICAL_FLG = "canonical_flg";
    public String CANONICAL_HEADER_FLAG = "canonical_header_flag";
    public String CANONICAL_HEADER_TYPE = "canonical_header_type";
    public String CANONICAL_TYPE = "canonical_type";
    public String CANONICAL_URL_IS_CONSISTENT = "canonical_url_is_consistent";
    public String CUSTOM_DATA = "custom_data";
    public String DOCUMENT_SIZE = "document_size";
    public String DOWNLOAD_LATENCY = "download_latency";
    public String DOWNLOAD_TIME = "download_time";
    public String H1_LENGTH = "h1_length";
    public String H1_MD5 = "h1_md5";
    public String HREFLANG_LINKS = "hreflang_links";
    public String HREFLANG_LINKS_OUT_COUNT = "hreflang_links_out_count";
    public String HREFLANG_URL_COUNT = "hreflang_url_count";
    public String INDEX_FLG = "index_flg";
    public String INDEX_FLG_X_TAG = "index_flg_x_tag";
    public String INDEXABLE = "indexable";
    public String OG_MARKUP = "og_markup";
    public String OG_MARKUP_FLAG = "og_markup_flag";
    public String OG_MARKUP_LENGTH = "og_markup_length";
    public String OUTLINK_COUNT = "outlink_count";
    public String PAGE_1 = "page_1";
    public String ALTERNATE_LINKS = "alternate_links";
    public String PAGE_LINK = "page_link";
    public String PAGE_TIMEOUT_FLAG = "page_timeout_flag";
    public String PAGINATED = "paginated";
    public String PAGINATION_LINKS = "pagination_links";
    public String PROTOCOL = "protocol";
    public String REDIRECT_BLOCKED = "redirect_blocked";
    public String REDIRECT_BLOCKED_REASON = "redirect_blocked_reason";
    public String REDIRECT_CHAIN = "redirect_chain";
    public String RESPONSE_HEADERS = "response_headers";
    public String ROBOTS_CONTENTS = "robots_contents";
    public String ROBOTS_CONTENTS_X_TAG = "robots_contents_x_tag";
    public String TITLE_FLG = "title_flg";
    public String TITLE_LENGTH = "title_length";
    public String TITLE_MD5 = "title_md5";
    public String TWITTER_DESCRIPTION_LENGTH = "twitter_description_length";
    public String TWITTER_MARKUP = "twitter_markup";
    public String TWITTER_MARKUP_FLAG = "twitter_markup_flag";
    public String TWITTER_MARKUP_LENGTH = "twitter_markup_length";
    public String URL_LENGTH = "url_length";
    public String VALID_TWITTER_CARD = "valid_twitter_card";
    public String VIEWPORT_CONTENT = "viewport_content";
    public String COUNT_OF_OBJECTS = "count_of_objects";
    public String PAGE_ANALYSIS_RESULTS = "page_analysis_results";
    public String CHANGE_TRACKING_HASH = "change_tracking_hash";
    public String LOWER_CASE_URL_HASH = "lower_case_url_hash";
    public String URL_MURMUR_HASH = "url_murmur_hash";
    public String H1 = "h1";
    public String H1_COUNT = "h1_count";
    public String H2 = "h2";
    public String META_CHARSET = "meta_charset";
    public String REDIRECT_FINAL_URL = "redirect_final_url";
    public String RESPONSE_CODE = "response_code";
    public String CRAWL_TIMESTAMP = "crawl_timestamp";
    public String URL_HASH = "url_hash";
    public String TITLE = "title";

    public static List<String> getChangeTrackingFieldNames() {
        List<String> changeTrackingFieldNames = new ArrayList<String>();
        changeTrackingFieldNames.add("alternate_links");
        changeTrackingFieldNames.add("amphtml_flag");
        changeTrackingFieldNames.add("amphtml_href");
        changeTrackingFieldNames.add("analyzed_url_flg_s");
        changeTrackingFieldNames.add("analyzed_url_s");
        changeTrackingFieldNames.add("archive_flg");
        changeTrackingFieldNames.add("archive_flg_x_tag");
        changeTrackingFieldNames.add("blocked_by_robots");
        changeTrackingFieldNames.add("canonical");
        changeTrackingFieldNames.add("canonical_flg");
        changeTrackingFieldNames.add("canonical_header_flag");
        changeTrackingFieldNames.add("canonical_header_type");
        changeTrackingFieldNames.add("canonical_type");
        changeTrackingFieldNames.add("canonical_url_is_consistent");
        changeTrackingFieldNames.add("content_type");
        changeTrackingFieldNames.add("custom_data");
        changeTrackingFieldNames.add("description");
        changeTrackingFieldNames.add("description_flg");
        changeTrackingFieldNames.add("description_length");
        changeTrackingFieldNames.add("description_simhash");
        changeTrackingFieldNames.add("error_message");
        changeTrackingFieldNames.add("final_response_code");
        changeTrackingFieldNames.add("follow_flg");
        changeTrackingFieldNames.add("follow_flg_x_tag");
        changeTrackingFieldNames.add("h1");
        changeTrackingFieldNames.add("h1_count");
        changeTrackingFieldNames.add("h1_flg");
        changeTrackingFieldNames.add("h1_length");
        changeTrackingFieldNames.add("h1_md5");
        changeTrackingFieldNames.add("h2");
        changeTrackingFieldNames.add("header_noarchive");
        changeTrackingFieldNames.add("header_nofollow");
        changeTrackingFieldNames.add("header_noindex");
        changeTrackingFieldNames.add("header_noodp");
        changeTrackingFieldNames.add("header_nosnippet");
        changeTrackingFieldNames.add("header_noydir");
        changeTrackingFieldNames.add("hreflang_errors");
        changeTrackingFieldNames.add("hreflang_links");
        changeTrackingFieldNames.add("hreflang_links_out_count");
        changeTrackingFieldNames.add("hreflang_url_count");
        changeTrackingFieldNames.add("index_flg");
        changeTrackingFieldNames.add("index_flg_x_tag");
        changeTrackingFieldNames.add("indexable");
        changeTrackingFieldNames.add("insecure_resources");
        changeTrackingFieldNames.add("insecure_resources_flag");
        changeTrackingFieldNames.add("meta_charset");
        changeTrackingFieldNames.add("meta_content_type");
        changeTrackingFieldNames.add("meta_disabled_sitelinks");
        changeTrackingFieldNames.add("meta_noodp");
        changeTrackingFieldNames.add("meta_nosnippet");
        changeTrackingFieldNames.add("meta_noydir");
        changeTrackingFieldNames.add("meta_redirect");
        changeTrackingFieldNames.add("mixed_redirects");
        changeTrackingFieldNames.add("mobile_rel_alternate_url_is_consistent");
        changeTrackingFieldNames.add("noodp");
        changeTrackingFieldNames.add("nosnippet");
        changeTrackingFieldNames.add("noydir");
        changeTrackingFieldNames.add("og_markup");
        changeTrackingFieldNames.add("og_markup_flag");
        changeTrackingFieldNames.add("og_markup_length");
        changeTrackingFieldNames.add("outlink_count");
        changeTrackingFieldNames.add("page_link");
        changeTrackingFieldNames.add("page_analysis_results");
        changeTrackingFieldNames.add("redirect_blocked");
        changeTrackingFieldNames.add("redirect_blocked_reason");
        changeTrackingFieldNames.add("redirect_chain");
        changeTrackingFieldNames.add("redirect_final_url");
        changeTrackingFieldNames.add("redirect_flg");
        changeTrackingFieldNames.add("redirect_times");
        changeTrackingFieldNames.add("response_code");
        changeTrackingFieldNames.add("response_headers");
        changeTrackingFieldNames.add("robots");
        changeTrackingFieldNames.add("robots_contents");
        changeTrackingFieldNames.add("robots_contents_x_tag");
        changeTrackingFieldNames.add("robots_flg");
        changeTrackingFieldNames.add("robots_flg_x_tag");
        changeTrackingFieldNames.add("structured_data");
        changeTrackingFieldNames.add("title");
        changeTrackingFieldNames.add("title_flg");
        changeTrackingFieldNames.add("title_length");
        changeTrackingFieldNames.add("title_md5");
        changeTrackingFieldNames.add("title_simhash");
        changeTrackingFieldNames.add("viewport_content");
        changeTrackingFieldNames.add("viewport_flag");
        changeTrackingFieldNames.add("base_tag");
        changeTrackingFieldNames.add("base_tag_flag");
        changeTrackingFieldNames.add("base_tag_target");
        changeTrackingFieldNames.add("page_analysis_fragments");
        return changeTrackingFieldNames;
    }

    public static String getSortedCharactersHashCode(Object inputObject) {
        String hashCode = null;
        String inputString = null;
        byte[] unsortedByteArray = null;
        List<Byte> byteList = null;
        String sortedCharactersString = null;
        try {
            if (inputObject != null) {
                inputString = inputObject.toString();
                if (StringUtils.isNotBlank(inputString)) {
                    unsortedByteArray = inputString.getBytes("UTF-8");
                    byteList = new ArrayList<Byte>();
                    for (Byte testByte : unsortedByteArray) {
                        byteList.add(testByte);
                    }
                    Collections.sort(byteList);
                    sortedCharactersString = byteList.toString();
                    hashCode = getMd5HashCode(sortedCharactersString);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hashCode;
    }

    public static String getMd5HashCode(String plainText) {
        String result = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes());
            byte b[] = md.digest();
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("");
                buf.append(Integer.toHexString(i));
            }
            result = buf.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return result;
    }

    public List<HtmlChange> trackChanges(String ip, HtmlClickHouseEntity htmlCurrentEntity, HtmlClickHouseEntity previousEntity,
                                         JSONObject previousMd5JsonObject, JSONObject currentMd5JsonObject,
                                         ChangeTrackingHashCdJson[] currentChangeTrackingHashCdJsonArray, String previousResponseCodeInCickHouse, Date previousTrackDateInCickHouse, int crawlType,
                                         Boolean urlSkipDomainNameFlg, Boolean textCaseInsensitiveFlg) {
        boolean isDifferent = false;
        List<String> changeTrackingFields = getChangeTrackingFieldNames();
        String previousChangeTrackingHashCode = null;
        String currentChangeTrackingHashCode = null;
        String trackDateStringInClickHouse = null;
        String trackDateStringInQueueMessage = null;
        PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray = null;
        String pageAnalysisResultsChgIndJson = null;
        String[] previousStringArray = null;
        String[] currentStringArray = null;
        boolean isCustomDataAvailable = false;
        OgMarkup[] ogMarkup = null;
        String json = null;
        PageLink[] pageLink = null;
        String[] currentResponseHeaderNames = null;
        List<HtmlChange> htmlChangeList = new ArrayList<HtmlChange>();

        // when competitor URLs
        final Integer previousResponseCode = previousEntity.getResponse_code();


        // when previous HTTP status code is different from current HTTP status code
        final Integer currentResponseCode = htmlCurrentEntity.getResponse_code();
        if (isDifferent(currentResponseCode, previousResponseCode)) {
            final HtmlChange responseCodeChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
            responseCodeChange.setChgId(ChangeIndicatorEnum.RESPONSE_CODE_CHG_IND.id);
            responseCodeChange.setCurrValue(htmlCurrentEntity.getResponse_code().toString());
            responseCodeChange.setPrevValue(htmlCurrentEntity.getResponse_code().toString());
            htmlChangeList.add(responseCodeChange);

            // 404_detected_ind (ie. current is 404 and previous is not 4xx)
            if (currentResponseCode == 404 && previousResponseCode != 404) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.STATUS_404_DETECTED_IND.id);
                htmlChange.setCurrValue("404");
                htmlChange.setPrevValue(previousResponseCode.toString());
                htmlChangeList.add(htmlChange);
            }

            // 404_removed_ind (ie. current is not 4xx and previous is 404)
            if (currentResponseCode != 404 && previousResponseCode == 404) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.STATUS_404_REMOVED_IND.id);
                htmlChange.setCurrValue(currentResponseCode.toString());
                htmlChange.setPrevValue("404");
                htmlChangeList.add(htmlChange);
            }

            // redirect_301_detected_ind (ie. current is 301 and previous is not 3xx)
            if (currentResponseCode == 301 && (previousResponseCode < 300 || previousResponseCode > 399)) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_301_DETECTED_IND.id);
                htmlChange.setCurrValue("301");
                htmlChange.setPrevValue(previousResponseCode.toString());
                htmlChangeList.add(htmlChange);
            }

            // redirect_301_removed_ind (ie. current is not 3xx and previous is 301)
            if ((currentResponseCode < 300 || currentResponseCode > 399) && previousResponseCode == 301) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_301_REMOVED_IND.id);
                htmlChange.setCurrValue(currentResponseCode.toString());
                htmlChange.setPrevValue("301");
                htmlChangeList.add(htmlChange);
            }

            // redirect_302_detected_ind (ie. current is 302 and previous is not 3xx)
            if (currentResponseCode == 302 && (previousResponseCode < 300 || previousResponseCode > 399)) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_302_DETECTED_IND.id);
                htmlChange.setCurrValue("302");
                htmlChange.setPrevValue(previousResponseCode.toString());
                htmlChangeList.add(htmlChange);
            }

            // redirect_302_removed_ind (ie. current is not 3xx and previous is 302)
            if ((currentResponseCode < 300 || currentResponseCode > 399) && previousResponseCode == 302) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_302_REMOVED_IND.id);
                htmlChange.setCurrValue(currentResponseCode.toString());
                htmlChange.setPrevValue("302");
                htmlChangeList.add(htmlChange);
            }

            // redirect_diff_code_ind (current is 3xx and previous is 3xx and current != previous)
            if (previousResponseCode >= 300 && previousResponseCode <= 399 && currentResponseCode >= 300 && currentResponseCode <= 399 && previousResponseCode != currentResponseCode) {
                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_DIFF_CODE_IND.id);
                htmlChange.setCurrValue(currentResponseCode.toString());
                htmlChange.setPrevValue(previousResponseCode.toString());
                htmlChangeList.add(htmlChange);
            }

            return htmlChangeList;
        }
        // when previous HTTP status code is same as current HTTP status code
        else {
            // when current HTTP status code is 3xx, track changes
            if (currentResponseCode >= 300 && currentResponseCode <= 399) {
                for (String changeTrackingField : changeTrackingFields) {
                    previousChangeTrackingHashCode = null;
                    currentChangeTrackingHashCode = null;
                    switch (changeTrackingField) {
                        // redirect_blocked
                        case "redirect_blocked": {
                            final String currentRedirectBlocked = currentMd5JsonObject.getString("redirect_blocked");
                            final String previousRedirectBlocked = previousMd5JsonObject.getString("redirect_blocked");
                            if (!StringUtils.equalsIgnoreCase(currentRedirectBlocked, previousRedirectBlocked)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_BLOCKED_CHG_IND.id);
                                htmlChange.setCurrValue(String.valueOf(htmlCurrentEntity.getRedirect_blocked()));
                                htmlChange.setPrevValue(String.valueOf(previousEntity.getRedirect_blocked()));
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // redirect_blocked_reason
                        case "redirect_blocked_reason": {
                            final String currentRedirectBlockedReason = currentMd5JsonObject.getString("redirect_blocked_reason");
                            final String previousRedirectBlockedReason = previousMd5JsonObject.getString("redirect_blocked_reason");
                            if (!StringUtils.equalsIgnoreCase(currentRedirectBlockedReason, previousRedirectBlockedReason)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_BLOCKED_REASON_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getRedirect_blocked_reason());
                                htmlChange.setPrevValue(previousEntity.getRedirect_blocked_reason());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // redirect_chain
                        case "redirect_chain": {
                            final String previousRedirectChain = previousMd5JsonObject.getString("redirect_chain");
                            final String currentRedirectChain = currentMd5JsonObject.getString("redirect_chain");
                            if (!StringUtils.equalsIgnoreCase(currentRedirectChain, previousRedirectChain)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_CHAIN_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getRedirect_chain());
                                htmlChange.setPrevValue(previousEntity.getRedirect_chain());
                                htmlChangeList.add(htmlChange);
                                if (currentResponseCode == 301) {
                                    final HtmlChange htmlChange301 = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange301.setChgId(ChangeIndicatorEnum.REDIRECT_301_DETECTED_IND.id);
                                    htmlChange301.setCurrValue(htmlCurrentEntity.getResponse_code() + MULTI_STRING_SEPARATOR + htmlCurrentEntity.getRedirect_final_url());
                                    htmlChange301.setPrevValue(previousEntity.getResponse_code() + MULTI_STRING_SEPARATOR + previousEntity.getRedirect_final_url());
                                    htmlChangeList.add(htmlChange301);
                                } else if (currentResponseCode == 302) {
                                    final HtmlChange htmlChange302 = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange302.setChgId(ChangeIndicatorEnum.REDIRECT_302_DETECTED_IND.id);
                                    htmlChange302.setCurrValue(htmlCurrentEntity.getResponse_code() + MULTI_STRING_SEPARATOR + htmlCurrentEntity.getRedirect_final_url());
                                    htmlChange302.setPrevValue(previousEntity.getResponse_code() + MULTI_STRING_SEPARATOR + previousEntity.getRedirect_final_url());
                                    htmlChangeList.add(htmlChange302);
                                }

                            }

                        }
                        // redirect_final_url
                        case "redirect_final_url": {
                            final String previousRedirectFinalUrl = previousMd5JsonObject.getString("redirect_final_url");
                            final String currentRedirectFinalUrl = currentMd5JsonObject.getString("redirect_final_url");
                            if (!StringUtils.equalsIgnoreCase(currentRedirectFinalUrl, previousRedirectFinalUrl)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_FINAL_URL_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getRedirect_final_url());
                                htmlChange.setPrevValue(previousEntity.getRedirect_final_url());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // redirect_times
                        case "redirect_times": {
                            final String previousRedirectTimes = previousMd5JsonObject.getString("redirect_times");
                            final String currentRedirectTimes = currentMd5JsonObject.getString("redirect_times");
                            if (!StringUtils.equalsIgnoreCase(currentRedirectTimes, previousRedirectTimes)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.REDIRECT_TIMES_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getRedirect_times().toString());
                                htmlChange.setPrevValue(previousEntity.getRedirect_times().toString());
                                htmlChangeList.add(htmlChange);
                                isDifferent = true;
                            }
                            break;
                        }
                        default:
                            break;
                    }
                }
            }
            // when current HTTP status code is 200, track changes
            else if (currentResponseCode == 200) {
                for (String changeTrackingField : changeTrackingFields) {
                    previousChangeTrackingHashCode = null;
                    currentChangeTrackingHashCode = null;
                    switch (changeTrackingField) {
                        // alternate_links
                        case "alternate_links": {
                            final String currentMd5AlternateLinks = currentMd5JsonObject.getString("alternate_links");
                            final String previousMd5AlternateLinks = previousMd5JsonObject.getString("alternate_links");
                            if (!StringUtils.equalsIgnoreCase(currentMd5AlternateLinks, previousMd5AlternateLinks)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.ALTERNATE_LINKS_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getAlternate_links());
                                htmlChange.setPrevValue(previousEntity.getAlternate_links());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // amphtml_href
                        case "amphtml_href": {
                            if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                nextCurrentChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "amphtml_href") == true) {
                                        currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextCurrentChangeTrackingHashCdJson;
                                    }
                                }
                            }
                            if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                nextPreviousChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "amphtml_href") == true) {
                                        previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextPreviousChangeTrackingHashCdJson;
                                    }
                                }
                            }

                            if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                htmlCurrentEntity.setAmphtmlHrefChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // analyzed_url_s
                        case "analyzed_url_s": {
                            final String previousAnalyzedUrlSMd5 = previousMd5JsonObject.getString("analyzed_url_s");
                            final String currentAnalyzedUrlSMd5 = currentMd5JsonObject.getString("analyzed_url_s");
                            if (!StringUtils.equalsIgnoreCase(currentAnalyzedUrlSMd5, previousAnalyzedUrlSMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.ANALYZED_URL_S_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getAnalyzed_url_s());
                                htmlChange.setPrevValue(previousEntity.getAnalyzed_url_s());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // archive_flg
                        case "archive_flg": {
                            final String previousArchiveFlgMd5 = previousMd5JsonObject.getString("archive_flg");
                            final String currentArchiveFlgMd5 = currentMd5JsonObject.getString("archive_flg");
                            if (!StringUtils.equalsIgnoreCase(currentArchiveFlgMd5, previousArchiveFlgMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.ARCHIVE_FLG_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getArchive_flg());
                                htmlChange.setPrevValue(previousEntity.getArchive_flg());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // archive_flg_x_tag
                        case "archive_flg_x_tag": {
                            final String previousArchiveFlgXTagMd5 = previousMd5JsonObject.getString("archive_flg_x_tag");
                            final String currentArchiveFlgXTagMd5 = currentMd5JsonObject.getString("archive_flg_x_tag");
                            if (!StringUtils.equalsIgnoreCase(currentArchiveFlgXTagMd5, previousArchiveFlgXTagMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.ARCHIVE_FLG_X_TAG_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getArchive_flg_x_tag());
                                htmlChange.setPrevValue(previousEntity.getArchive_flg_x_tag());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // blocked_by_robots
                        case "blocked_by_robots": {
                            final String previousBlockedByRobotsMd5 = previousMd5JsonObject.getString("blocked_by_robots");
                            final String currentBlockedByRobotsMd5 = currentMd5JsonObject.getString("blocked_by_robots");
                            if (!StringUtils.equalsIgnoreCase(currentBlockedByRobotsMd5, previousBlockedByRobotsMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.BLOCKED_BY_ROBOTS_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getBlocked_by_robots());
                                htmlChange.setPrevValue(previousEntity.getBlocked_by_robots());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // canonical
                        case "canonical": {
                            final String previousCanonicalFlgMd5 = previousMd5JsonObject.getString("canonical_flg");
                            final String currentCanonicalFlgMd5 = currentMd5JsonObject.getString("canonical_flg");
                            if (!StringUtils.equalsIgnoreCase(currentCanonicalFlgMd5, previousCanonicalFlgMd5)) {
                                final String previousCanonicalMd5 = previousMd5JsonObject.getString("canonical");
                                final String currentCanonicalMd5 = currentMd5JsonObject.getString("canonical");
                                if (!StringUtils.equalsIgnoreCase(currentCanonicalMd5, previousCanonicalMd5)) {
                                    final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_CHG_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getCanonical());
                                    htmlChange.setPrevValue(previousEntity.getCanonical());
                                    htmlChangeList.add(htmlChange);
                                }
                            }
                            break;
                        }
                        // canonical_flg
                        case "canonical_flg": {
                            final String previousCanonicalFlgMd5 = previousMd5JsonObject.getString("canonical_flg");
                            final String currentCanonicalFlgMd5 = currentMd5JsonObject.getString("canonical_flg");
                            if (!StringUtils.equalsIgnoreCase(currentCanonicalFlgMd5, previousCanonicalFlgMd5)) {
                                final Integer previousCanonicalFlg = previousEntity.getCanonical_flg();
                                final Integer currentCanonicalFlg = htmlCurrentEntity.getCanonical_flg();
                                if (previousCanonicalFlg == 1 && currentCanonicalFlg == 0) {
                                    final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_REMOVED_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getCanonical_flg().toString());
                                    htmlChange.setPrevValue(previousEntity.getCanonical_flg().toString());
                                    htmlChangeList.add(htmlChange);
                                } else if (previousCanonicalFlg == 0 && currentCanonicalFlg == 1) {
                                    final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_ADDED_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getCanonical_flg().toString());
                                    htmlChange.setPrevValue(previousEntity.getCanonical_flg().toString());
                                    htmlChangeList.add(htmlChange);
                                }

                            }
                            break;
                        }
                        // canonical_header_flag
                        case "canonical_header_flag": {
                            final String previousCanonicalHeaderFlagMd5 = previousMd5JsonObject.getString("canonical_header_flag");
                            final String currentCanonicalHeaderFlagMd5 = currentMd5JsonObject.getString("canonical_header_flag");
                            if (!StringUtils.equalsIgnoreCase(currentCanonicalHeaderFlagMd5, previousCanonicalHeaderFlagMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_HEADER_FLAG_CHG_IND.id);
                                htmlChange.setCurrValue(String.valueOf(htmlCurrentEntity.getCanonical_header_flag()));
                                htmlChange.setPrevValue(String.valueOf(previousEntity.getCanonical_header_flag()));
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // canonical_header_type
                        case "canonical_header_type": {
                            final String previousCanonicalHeaderTypeMd5 = previousMd5JsonObject.getString("canonical_header_type");
                            final String currentCanonicalHeaderTypeMd5 = currentMd5JsonObject.getString("canonical_header_type");
                            if (!StringUtils.equalsIgnoreCase(currentCanonicalHeaderTypeMd5, previousCanonicalHeaderTypeMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_HEADER_TYPE_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getCanonical_header_type());
                                htmlChange.setPrevValue(previousEntity.getCanonical_header_type());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // canonical_type
                        case "canonical_type": {
                            final String previousCanonicalTypeMd5 = previousMd5JsonObject.getString("canonical_type");
                            final String currentCanonicalTypeMd5 = currentMd5JsonObject.getString("canonical_type");
                            if (!StringUtils.equalsIgnoreCase(currentCanonicalTypeMd5, previousCanonicalTypeMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_TYPE_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getCanonical_type());
                                htmlChange.setPrevValue(previousEntity.getCanonical_type());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // canonical_url_is_consistent
                        case "canonical_url_is_consistent": {
                            final String previousCanonicalUrlIsConsistentMd5 = previousMd5JsonObject.getString("canonical_url_is_consistent");
                            final String currentCanonicalUrlIsConsistentMd5 = currentMd5JsonObject.getString("canonical_url_is_consistent");
                            if (!StringUtils.equalsIgnoreCase(currentCanonicalUrlIsConsistentMd5, previousCanonicalUrlIsConsistentMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.CANONICAL_URL_IS_CONSISTENT_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getCanonical_url_is_consistent());
                                htmlChange.setPrevValue(previousEntity.getCanonical_url_is_consistent());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // content_type
                        case "content_type": {
                            final String previousContentTypeMd5 = previousMd5JsonObject.getString("content_type");
                            final String currentContentTypeMd5 = currentMd5JsonObject.getString("content_type");
                            if (!StringUtils.equalsIgnoreCase(currentContentTypeMd5, previousContentTypeMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.CONTENT_TYPE_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getContent_type());
                                htmlChange.setPrevValue(previousEntity.getContent_type());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // custom_data
                        case "custom_data": {
                            final String previousCustomDataMd5 = previousMd5JsonObject.getString("custom_data");
                            final String currentCustomDataMd5 = currentMd5JsonObject.getString("custom_data");
                            if (StringUtils.equalsIgnoreCase(currentCustomDataMd5, previousCustomDataMd5)) {
                                break;
                            }
                            // check current custom_data is available
                            final boolean isCurrentCustomDataAvailable = checkIfCustomDataAvailable(htmlCurrentEntity.getCustom_data());
                            if (!isCurrentCustomDataAvailable && StringUtils.isBlank(previousCustomDataMd5)) {
                                break;
                            }
                            if (!StringUtils.equalsIgnoreCase(currentCustomDataMd5, previousCustomDataMd5)) {
                                final String currentCustomData = htmlCurrentEntity.getCustom_data();
                                final String previousCustomData = previousEntity.getCustom_data();
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setCurrValue(currentCustomData);
                                htmlChange.setPrevValue(previousCustomData);
                                if (checkIfJsonDataAvailable(previousCustomDataMd5) && checkIfJsonDataAvailable(currentCustomDataMd5)) {
                                    htmlChange.setChgId(ChangeIndicatorEnum.CUSTOM_DATA_CHG_IND.id);
                                } else if (checkIfJsonDataAvailable(currentCustomDataMd5) && !checkIfJsonDataAvailable(previousCustomDataMd5)) {
                                    htmlChange.setChgId(ChangeIndicatorEnum.CUSTOM_DATA_ADDED_IND.id);
                                } else if (!checkIfJsonDataAvailable(currentCustomDataMd5) && checkIfJsonDataAvailable(previousCustomDataMd5)) {
                                    htmlChange.setChgId(ChangeIndicatorEnum.CUSTOM_DATA_REMOVED_IND.id);
                                }
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // description
                        case "description": {
                            final String previousDescriptionMd5 = previousMd5JsonObject.getString("description");
                            final String currentDescriptionMd5 = currentMd5JsonObject.getString("description");
                            if (!StringUtils.equalsIgnoreCase(currentDescriptionMd5, previousDescriptionMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.DESCRIPTION_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getDescription());
                                htmlChange.setPrevValue(previousEntity.getDescription());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // description_flg
                        case "description_flg": {
                            final String previousDescriptionFlgMd5 = previousMd5JsonObject.getString("description_flg");
                            final String currentDescriptionFlgMd5 = currentMd5JsonObject.getString("description_flg");
                            if (!StringUtils.equalsIgnoreCase(currentDescriptionFlgMd5, previousDescriptionFlgMd5)) {
                                final Integer previousDescriptionFlg = previousEntity.getDescription_flg();
                                final Integer currentDescriptionFlg = htmlCurrentEntity.getDescription_flg();
                                if (currentDescriptionFlg == 1 && previousDescriptionFlg != 1) {
                                    final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.DESCRIPTION_ADDED_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getDescription_flg().toString());
                                    htmlChange.setPrevValue(previousEntity.getDescription_flg().toString());
                                    htmlChangeList.add(htmlChange);
                                } else if (currentDescriptionFlg == 0 && previousDescriptionFlg == 0) {
                                    final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.DESCRIPTION_REMOVED_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getDescription_flg().toString());
                                    htmlChange.setPrevValue(previousEntity.getDescription_flg().toString());
                                    htmlChangeList.add(htmlChange);
                                }

                            }
                            break;
                        }
                        // description_length
                        case "description_length": {
                            final String previousDescriptionLengthMd5 = previousMd5JsonObject.getString("description_length");
                            final String currentDescriptionLengthMd5 = currentMd5JsonObject.getString("description_length");
                            if (!StringUtils.equalsIgnoreCase(currentDescriptionLengthMd5, previousDescriptionLengthMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.DESCRIPTION_LENGTH_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getDescription_length().toString());
                                htmlChange.setPrevValue(previousEntity.getDescription_length().toString());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // final_response_code
                        case "final_response_code": {
                            final String previousFinalResponseCodeMd5 = previousMd5JsonObject.getString("final_response_code");
                            final String currentFinalResponseCodeMd5 = currentMd5JsonObject.getString("final_response_code");
                            if (!StringUtils.equalsIgnoreCase(currentFinalResponseCodeMd5, previousFinalResponseCodeMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.FINAL_RESPONSE_CODE_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getFinal_response_code().toString());
                                htmlChange.setPrevValue(previousEntity.getFinal_response_code().toString());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // follow_flg
                        case "follow_flg": {
                            final String previousFollowFlgMd5 = previousMd5JsonObject.getString("follow_flg");
                            final String currentFollowFlgMd5 = currentMd5JsonObject.getString("follow_flg");
                            if (!StringUtils.equalsIgnoreCase(currentFollowFlgMd5, previousFollowFlgMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.FOLLOW_FLG_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getFollow_flg().toString());
                                htmlChange.setPrevValue(previousEntity.getFollow_flg().toString());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // h1
                        case "h1": {
                            final String previousH1Md5 = previousMd5JsonObject.getString("h1");
                            final String currentH1Md5 = currentMd5JsonObject.getString("h1");
                            if (!StringUtils.equalsIgnoreCase(currentH1Md5, previousH1Md5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.H1_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getH1());
                                htmlChange.setPrevValue(previousEntity.getH1());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // h1_count
                        case "h1_count": {
                            final String previousH1CountMd5 = previousMd5JsonObject.getString("h1_count");
                            final String currentH1CountMd5 = currentMd5JsonObject.getString("h1_count");
                            if (!StringUtils.equalsIgnoreCase(currentH1CountMd5, previousH1CountMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.H1_COUNT_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getH1_count().toString());
                                htmlChange.setPrevValue(previousEntity.getH1_count().toString());
                                htmlChangeList.add(htmlChange);
                                final int previousH1Count = previousEntity.getH1_count() != null ? previousEntity.getH1_count().intValue() : 0;
                                final int currentH1Count = htmlCurrentEntity.getH1_count() != null ? htmlCurrentEntity.getH1_count().intValue() : 0;
                                final int changeInteger = currentH1Count - previousH1Count;
                                if (changeInteger > 0) {
                                    final HtmlChange htmlChangeH1Added = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.H1_ADDED_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getH1_count().toString());
                                    htmlChange.setPrevValue(previousEntity.getH1_count().toString());
                                    htmlChangeList.add(htmlChange);
                                } else if (changeInteger < 0) {
                                    final HtmlChange htmlChangeH1Removed = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                    htmlChange.setChgId(ChangeIndicatorEnum.H1_REMOVED_IND.id);
                                    htmlChange.setCurrValue(htmlCurrentEntity.getH1_count().toString());
                                    htmlChange.setPrevValue(previousEntity.getH1_count().toString());
                                    htmlChangeList.add(htmlChange);
                                }
                            }
                            break;
                        }
                        // h1_length
                        case "h1_length": {
                            final String previousH1LengthMd5 = previousMd5JsonObject.getString("h1_length");
                            final String currentH1LengthMd5 = currentMd5JsonObject.getString("h1_length");
                            if (!StringUtils.equalsIgnoreCase(currentH1LengthMd5, previousH1LengthMd5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                htmlChange.setChgId(ChangeIndicatorEnum.H1_LENGTH_CHG_IND.id);
                                htmlChange.setCurrValue(htmlCurrentEntity.getH1_length().toString());
                                htmlChange.setPrevValue(previousEntity.getH1_length().toString());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // h2
                        case "h2": {
                            final String previousH2Md5 = previousMd5JsonObject.getString("h2");
                            final String currentH2Md5 = currentMd5JsonObject.getString("h2");
                            if (!StringUtils.equalsIgnoreCase(currentH2Md5, previousH2Md5)) {
                                final HtmlChange htmlChange = HtmlChange.createBaseHtmlChangeFromEntities(previousEntity, htmlCurrentEntity);
                                // h2 is a String array in ClickHouse, so h2 count is the only way to determine if h2 was added or removed
                                final String h2 = previousEntity.getH2();
                                int previousH2Count;
                                try {
                                    previousH2Count = gson.fromJson(h2, String[].class).length;
                                } catch (JsonSyntaxException e) {
                                    previousH2Count = 0;
                                }
                                final String h2Current = htmlCurrentEntity.getH2();
                                int currentH2Count;
                                try {
                                    currentH2Count = gson.fromJson(h2Current, String[].class).length;
                                } catch (JsonSyntaxException e) {
                                    currentH2Count = 0;
                                }
                                final int changeInteger = currentH2Count - previousH2Count;
                                if (changeInteger == 0) {
                                    htmlChange.setChgId(ChangeIndicatorEnum.H2_CHG_IND.id);
                                } else if (changeInteger > 0) {
                                    htmlChange.setChgId(ChangeIndicatorEnum.H2_ADDED_IND.id);
                                } else {
                                    htmlChange.setChgId(ChangeIndicatorEnum.H2_REMOVED_IND.id);
                                }
                                htmlChange.setCurrValue(htmlCurrentEntity.getH2());
                                htmlChange.setPrevValue(previousEntity.getH2());
                                htmlChangeList.add(htmlChange);
                            }
                            break;
                        }
                        // header_noarchive
                        case "header_noarchive": {
                            if (isDifferent(htmlCurrentEntity.getHeader_noarchive(), previousEntity.getHeader_noarchive()) == true) {
                                htmlCurrentEntity.setHeaderNoarchiveChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // header_nofollow
                        case "header_nofollow": {
                            if (isDifferent(htmlCurrentEntity.getHeader_nofollow(), previousEntity.getHeader_nofollow()) == true) {
                                htmlCurrentEntity.setHeaderNofollowChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // header_noindex
                        case "header_noindex": {
                            if (isDifferent(htmlCurrentEntity.getHeader_noindex(), previousEntity.getHeader_noindex()) == true) {
                                htmlCurrentEntity.setHeaderNoindexChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // header_noodp
                        case "header_noodp": {
                            if (isDifferent(htmlCurrentEntity.getHeader_noodp(), previousEntity.getHeader_noodp()) == true) {
                                htmlCurrentEntity.setHeaderNoodpChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // header_nosnippet
                        case "header_nosnippet": {
                            if (isDifferent(htmlCurrentEntity.getHeader_nosnippet(), previousEntity.getHeader_nosnippet()) == true) {
                                htmlCurrentEntity.setHeaderNosnippetChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // header_noydir
                        case "header_noydir": {
                            if (isDifferent(htmlCurrentEntity.getHeader_noydir(), previousEntity.getHeader_noydir()) == true) {
                                htmlCurrentEntity.setHeaderNoydirChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // hreflang_errors
                        case "hreflang_errors": {
                            if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                nextCurrentChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "hreflang_errors") == true) {
                                        currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextCurrentChangeTrackingHashCdJson;
                                    }
                                }
                            }
                            if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                nextPreviousChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "hreflang_errors") == true) {
                                        previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextPreviousChangeTrackingHashCdJson;
                                    }
                                }
                            }

                            if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                htmlCurrentEntity.setHreflangErrorsChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // hreflang_links
                        case "hreflang_links": {
                            if (isDifferent(htmlCurrentEntity.getHreflang_url_count(), previousEntity.getHreflang_url_count()) == false) {
                                if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                    nextCurrentChangeTrackingHashCdJson:
                                    for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                        if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "hreflang_links") == true) {
                                            currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                            break nextCurrentChangeTrackingHashCdJson;
                                        }
                                    }
                                }
                                if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                    nextPreviousChangeTrackingHashCdJson:
                                    for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                        if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "hreflang_links") == true) {
                                            previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                            break nextPreviousChangeTrackingHashCdJson;
                                        }
                                    }
                                }

                                if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                    htmlCurrentEntity.setHreflangLinksChgInd(true);
                                    isDifferent = true;
                                }
                            }
                            break;
                        }
                        // hreflang_links_out_count
                        case "hreflang_links_out_count": {
                            if (isDifferent(htmlCurrentEntity.getHreflang_links_out_count(),
                                    previousEntity.getHreflang_links_out_count()) == true) {
                                htmlCurrentEntity.setHreflangLinksOutCountChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // hreflang_url_count
                        case "hreflang_url_count": {
                            if (isDifferent(htmlCurrentEntity.getHreflang_url_count(), previousEntity.getHreflang_url_count()) == true) {
                                htmlCurrentEntity.setHreflangUrlCountChgInd(true);
                                isDifferent = true;

                                previousResponseCode = previousEntity.getHreflang_url_count() != null ? previousEntity.getHreflang_url_count().intValue() : 0;
                                currentResponseCode = htmlCurrentEntity.getHreflang_url_count() != null
                                        ? htmlCurrentEntity.getHreflang_url_count().intValue()
                                        : 0;
                                changeInteger = currentResponseCode - previousResponseCode;

                                // hreflang_links_added_ind
                                if (changeInteger > 0) {
                                    htmlCurrentEntity.setHreflangLinksAddedInd(true);
                                }
                                // hreflang_links_removed_ind
                                else if (changeInteger < 0) {
                                    htmlCurrentEntity.setHreflangLinksRemovedInd(true);
                                }

                            }
                            break;
                        }
                        // index_flg
                        case "index_flg": {
                            if (isDifferent(htmlCurrentEntity.getIndex_flg(), previousEntity.getIndex_flg()) == true) {
                                htmlCurrentEntity.setIndexFlgChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // index_flg_x_tag
                        case "index_flg_x_tag": {
                            if (isDifferent(htmlCurrentEntity.getIndex_flg_x_tag(), previousEntity.getIndex_flg_x_tag()) == true) {
                                htmlCurrentEntity.setIndexFlgXTagChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // indexable
                        case "indexable": {
                            if (isDifferent(htmlCurrentEntity.getIndexable(), previousEntity.getIndexable()) == true) {
                                htmlCurrentEntity.setIndexableChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // insecure_resources
                        case "insecure_resources": {
                            if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                nextCurrentChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "insecure_resources") == true) {
                                        currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextCurrentChangeTrackingHashCdJson;
                                    }
                                }
                            }
                            if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                nextPreviousChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "insecure_resources") == true) {
                                        previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextPreviousChangeTrackingHashCdJson;
                                    }
                                }
                            }

                            if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                htmlCurrentEntity.setInsecureResourcesChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // insecure_resources_flag
                        case "insecure_resources_flag": {
                            if (isDifferent(htmlCurrentEntity.getInsecure_resources_flag(),
                                    previousEntity.getInsecure_resources_flag()) == true) {
                                htmlCurrentEntity.setInsecureResourcesFlagChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_charset
                        case "meta_charset": {
                            if (isDifferent(htmlCurrentEntity.getMeta_charset(), previousEntity.getMeta_charset()) == true) {
                                htmlCurrentEntity.setMetaCharsetChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_content_type
                        case "meta_content_type": {
                            if (isDifferent(htmlCurrentEntity.getMeta_content_type(), previousEntity.getMeta_content_type()) == true) {
                                htmlCurrentEntity.setMetaContentTypeChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_disabled_sitelinks
                        case "meta_disabled_sitelinks": {
                            if (isDifferent(htmlCurrentEntity.getMeta_disabled_sitelinks(),
                                    previousEntity.getMeta_disabled_sitelinks()) == true) {
                                htmlCurrentEntity.setMetaDisabledSitelinksChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_noodp
                        case "meta_noodp": {
                            if (isDifferent(htmlCurrentEntity.getMeta_noodp(), previousEntity.getMeta_noodp()) == true) {
                                htmlCurrentEntity.setMetaNoodpChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_nosnippet
                        case "meta_nosnippet": {
                            if (isDifferent(htmlCurrentEntity.getMeta_nosnippet(), previousEntity.getMeta_nosnippet()) == true) {
                                htmlCurrentEntity.setMetaNosnippetChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_noydir
                        case "meta_noydir": {
                            if (isDifferent(htmlCurrentEntity.getMeta_noydir(), previousEntity.getMeta_noydir()) == true) {
                                htmlCurrentEntity.setMetaNoydirChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // meta_redirect
                        case "meta_redirect": {
                            if (isDifferent(htmlCurrentEntity.getMeta_redirect(), previousEntity.getMeta_redirect()) == true) {
                                htmlCurrentEntity.setMetaRedirectChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // mixed_redirects
                        case "mixed_redirects": {
                            if (isDifferent(htmlCurrentEntity.getMixed_redirects(), previousEntity.getMixed_redirects()) == true) {
                                htmlCurrentEntity.setMixedRedirectsChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // mobile_rel_alternate_url_is_consistent
                        case "mobile_rel_alternate_url_is_consistent": {
                            if (isDifferent(htmlCurrentEntity.getMobile_rel_alternate_url_is_consistent(),
                                    previousEntity.getMobile_rel_alternate_url_is_consistent()) == true) {
                                htmlCurrentEntity.setMobileRelAlternateUrlIsConsistentChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // noodp
                        case "noodp": {
                            if (isDifferent(htmlCurrentEntity.getNoodp(), previousEntity.getNoodp()) == true) {
                                htmlCurrentEntity.setNoodpChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // nosnippet
                        case "nosnippet": {
                            if (isDifferent(htmlCurrentEntity.getNosnippet(), previousEntity.getNosnippet()) == true) {
                                htmlCurrentEntity.setNosnippetChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // noydir
                        case "noydir": {
                            if (isDifferent(htmlCurrentEntity.getNoydir(), previousEntity.getNoydir()) == true) {
                                htmlCurrentEntity.setNoydirChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // og_markup
                        case "og_markup": {
                            if (isDifferent(htmlCurrentEntity.getOg_markup_flag(), previousEntity.getOg_markup_flag()) == false) {
                                if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {

                                    previousChangeTrackingHashCode = null;
                                    currentChangeTrackingHashCode = null;

                                    // previous
                                    ogMarkup = previousEntity.getOg_markup();
                                    if (ogMarkup != null) {
                                        json = gson.toJson(ogMarkup, OgMarkup[].class);
                                        if (checkIfJsonDataAvailable(json) == true) {
                                            previousChangeTrackingHashCode = getSortedCharactersHashCode(json);
                                        }
                                    }

                                    // current
                                    ogMarkup = htmlCurrentEntity.getOg_markup();
                                    if (ogMarkup != null) {
                                        json = gson.toJson(ogMarkup, OgMarkup[].class);
                                        if (checkIfJsonDataAvailable(json) == true) {
                                            currentChangeTrackingHashCode = getSortedCharactersHashCode(json);
                                        }
                                    }
                                } else {
                                    if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                        nextCurrentChangeTrackingHashCdJson:
                                        for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                            if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "og_markup") == true) {
                                                currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                                break nextCurrentChangeTrackingHashCdJson;
                                            }
                                        }
                                    }
                                    if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                        nextPreviousChangeTrackingHashCdJson:
                                        for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                            if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "og_markup") == true) {
                                                previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                                break nextPreviousChangeTrackingHashCdJson;
                                            }
                                        }
                                    }
                                }

                                if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                    htmlCurrentEntity.setOgMarkupChgInd(true);
                                    isDifferent = true;
                                }
                            }
                            break;
                        }
                        // og_markup_flag
                        case "og_markup_flag": {
                            if (isDifferent(htmlCurrentEntity.getOg_markup_flag(), previousEntity.getOg_markup_flag()) == true) {
                                htmlCurrentEntity.setOgMarkupFlagChgInd(true);
                                isDifferent = true;

                                // open_graph_added_ind (ie. current is true and previous is false)
                                if (BooleanUtils.isTrue(htmlCurrentEntity.getOg_markup_flag())
                                        && BooleanUtils.isFalse(previousEntity.getOg_markup_flag())) {
                                    htmlCurrentEntity.setOpenGraphAddedInd(true);
                                }
                                // open_graph_removed_ind (ie. current is false and previous is true)
                                else if (BooleanUtils.isFalse(htmlCurrentEntity.getOg_markup_flag())
                                        && BooleanUtils.isTrue(previousEntity.getOg_markup_flag())) {
                                    htmlCurrentEntity.setOpenGraphRemovedInd(true);
                                }

                            }
                            break;
                        }
                        // og_markup_length
                        case "og_markup_length": {
                            if (isDifferent(htmlCurrentEntity.getOg_markup_length(), previousEntity.getOg_markup_length()) == true) {
                                htmlCurrentEntity.setOgMarkupLengthChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // outlink_count
                        case "outlink_count": {
                            if (isDifferent(htmlCurrentEntity.getOutlink_count(), previousEntity.getOutlink_count()) == true) {
                                htmlCurrentEntity.setOutlinkCountChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // page_link
                        case "page_link": {
                            if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {

                                previousChangeTrackingHashCode = null;
                                currentChangeTrackingHashCode = null;

                                // previous
                                pageLink = previousEntity.getPage_link();
                                if (pageLink != null) {
                                    json = gson.toJson(pageLink, PageLink[].class);
                                    if (checkIfJsonDataAvailable(json) == true) {
                                        previousChangeTrackingHashCode = getSortedCharactersHashCode(json);
                                    }
                                }

                                // current
                                pageLink = htmlCurrentEntity.getPage_link();
                                if (pageLink != null) {
                                    json = gson.toJson(pageLink, PageLink[].class);
                                    if (checkIfJsonDataAvailable(json) == true) {
                                        currentChangeTrackingHashCode = getSortedCharactersHashCode(json);
                                    }
                                }
                            } else {
                                if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                    nextCurrentChangeTrackingHashCdJson:
                                    for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                        if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "page_link") == true) {
                                            currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                            break nextCurrentChangeTrackingHashCdJson;
                                        }
                                    }
                                }
                                if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                    nextPreviousChangeTrackingHashCdJson:
                                    for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                        if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "page_link") == true) {
                                            previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                            break nextPreviousChangeTrackingHashCdJson;
                                        }
                                    }
                                }
                            }

                            if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                htmlCurrentEntity.setPageLinkChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // page_analysis_results
                        case "page_analysis_results": {

                            // when previous page analysis results is available in queue message (ie. pageAnalysisResultInd = true or false)
                            if (previousEntity.getPageAnalysisResultInd() != null) {
                                // when previous crawl did not have page analysis results (ie. pageAnalysisResultInd = false)
                                if (BooleanUtils.isFalse(previousEntity.getPageAnalysisResultInd())) {
                                    // when current crawl has page analysis results
                                    if (htmlCurrentEntity.getPageAnalysisResultArray() != null && htmlCurrentEntity.getPageAnalysisResultArray().length > 0) {
                                        isDifferent = true;
                                    }
                                }
                                // when previous crawl had page analysis results (ie. pageAnalysisResultInd = true)
                                else {
                                    // when current crawl does not have page analysis results
                                    if (htmlCurrentEntity.getPageAnalysisResultArray() == null || htmlCurrentEntity.getPageAnalysisResultArray().length == 0) {
                                        isDifferent = true;
                                    }
                                    // when current crawl has page analysis results
                                    else {
                                        pageAnalysisResultChgIndArray = getPageAnalysisResultChgIndArray(ip, htmlCurrentEntity.getUrl(),
                                                htmlCurrentEntity.getPageAnalysisResultArray(), previousEntity.getPageAnalysisResultArray());
                                        if (pageAnalysisResultChgIndArray != null && pageAnalysisResultChgIndArray.length > 0) {
                                            pageAnalysisResultsChgIndJson = transformPageAnalysisResultsChgIndArrayToString(pageAnalysisResultChgIndArray);
                                            if (StringUtils.isNotBlank(pageAnalysisResultsChgIndJson)) {
                                                htmlCurrentEntity.setPageAnalysisResultsChgIndJson(pageAnalysisResultsChgIndJson);
                                                isDifferent = true;
                                            }
                                        }
                                    }
                                }
                            } else {
                                // when previous page analysis results is not available in queue message, skip comparing current and previous page analysis results
                            }
                            break;
                        }
                        // robots
                        case "robots": {
                            if (isDifferent(htmlCurrentEntity.getRobots(), previousEntity.getRobots()) == true) {
                                htmlCurrentEntity.setRobotsChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // robots_contents
                        case "robots_contents": {
                            if (isDifferent(htmlCurrentEntity.getRobots_flg(), previousEntity.getRobots_flg()) == false
                                    && isDifferent(htmlCurrentEntity.getRobots_contents(), previousEntity.getRobots_contents()) == true) {
                                htmlCurrentEntity.setRobotsContentsChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // robots_contents_x_tag
                        case "robots_contents_x_tag": {
                            if (isDifferent(htmlCurrentEntity.getRobots_contents_x_tag(),
                                    previousEntity.getRobots_contents_x_tag()) == true) {
                                isDifferent = true;
                            }
                            break;
                        }
                        // robots_flg
                        case "robots_flg": {
                            if (isDifferent(htmlCurrentEntity.getRobots_flg(), previousEntity.getRobots_flg()) == true) {
                                htmlCurrentEntity.setRobotsFlgChgInd(true);
                                isDifferent = true;

                                // robots_added_ind (ie. current is Yes, previous is not Yes)
                                if (StringUtils.equalsIgnoreCase(htmlCurrentEntity.getRobots_flg(), "yes") == true
                                        && StringUtils.equalsIgnoreCase(previousEntity.getRobots_flg(), "yes") == false) {
                                    htmlCurrentEntity.setRobotsAddedInd(true);
                                }
                                // robots_removed_ind (ie. current is No, previous is not No)
                                else if (StringUtils.equalsIgnoreCase(htmlCurrentEntity.getRobots_flg(), "no") == true
                                        && StringUtils.equalsIgnoreCase(previousEntity.getRobots_flg(), "no") == false) {
                                    htmlCurrentEntity.setRobotsRemovedInd(true);
                                }
                            }
                            break;
                        }
                        // robots_flg_x_tag
                        case "robots_flg_x_tag": {
                            if (isDifferent(htmlCurrentEntity.getRobots_flg_x_tag(), previousEntity.getRobots_flg_x_tag()) == true) {
                                htmlCurrentEntity.setRobotsFlgXTagChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // https://www.wrike.com/open.htm?id=390204287
                        // structured_data
                        case "structured_data": {
                            if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                nextCurrentChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "structured_data") == true) {
                                        currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextCurrentChangeTrackingHashCdJson;
                                    }
                                }
                            }
                            if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                nextPreviousChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "structured_data") == true) {
                                        previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextPreviousChangeTrackingHashCdJson;
                                    }
                                }
                            }

                            if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                htmlCurrentEntity.setStructuredDataChgInd(true);
                                isDifferent = true;
                            }
                            break;
                        }
                        // title
                        case "title": {
                            if (isDifferent(htmlCurrentEntity.getTitle_flg(), previousEntity.getTitle_flg()) == false) {
                                if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
                                    if (isDifferent(htmlCurrentEntity.getTitle(), previousEntity.getTitle()) == true) {
                                        htmlCurrentEntity.setTitleChgInd(true);
                                        isDifferent = true;
                                    }
                                } else {
                                    if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                        nextCurrentChangeTrackingHashCdJson:
                                        for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                            if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "title") == true) {
                                                currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                                break nextCurrentChangeTrackingHashCdJson;
                                            }
                                        }
                                    }
                                    if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                        nextPreviousChangeTrackingHashCdJson:
                                        for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                            if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), "title") == true) {
                                                previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                                break nextPreviousChangeTrackingHashCdJson;
                                            }
                                        }
                                    }

                                    if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                        htmlCurrentEntity.setTitleChgInd(true);
                                        isDifferent = true;
                                    }
                                }
                            }
                            break;
                        }
                        // title_flg
                        case "title_flg": {
                            htmlCurrentEntity.setTitleFlgChgInd(true);
                            isDifferent = true;

                            // title_added_ind (ie. current is Yes, previous is not Yes)
                            if (StringUtils.equalsIgnoreCase(htmlCurrentEntity.getTitle_flg(), IConstants.YES) == true
                                    && StringUtils.equalsIgnoreCase(previousEntity.getTitle_flg(), IConstants.YES) == false) {
                                htmlCurrentEntity.setTitleAddedInd(true);
                            }
                            // title_removed_ind (ie. current is No, previous is not No)
                            else if (StringUtils.equalsIgnoreCase(htmlCurrentEntity.getTitle_flg(), IConstants.NO) == true
                                    && StringUtils.equalsIgnoreCase(previousEntity.getTitle_flg(), IConstants.NO) == false) {
                                htmlCurrentEntity.setTitleRemovedInd(true);
                            }
                        }
                        break;
                    }
                    // title_length
                    case IConstants.TITLE_LENGTH: {
                        if (isDifferent(htmlCurrentEntity.getTitle_length(), previousEntity.getTitle_length()) == true) {
                            htmlCurrentEntity.setTitleLengthChgInd(true);
                            isDifferent = true;
                        }
                        break;
                    }
                    // title_md5
                    case IConstants.TITLE_MD5: {
                        if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
                            // when text comparision is case insensitive, skip md5 comparison
                        } else {
                            if (isDifferent(htmlCurrentEntity.getTitle_md5(), previousEntity.getTitle_md5()) == true) {
                                htmlCurrentEntity.setTitleMd5ChgInd(true);
                                isDifferent = true;
                            }
                        }
                        break;
                    }
                    // title_simhash
                    case IConstants.TITLE_SIMHASH: {
                        if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
                            // when text comparision is case insensitive, skip simhash comparison
                        } else {
                            if (isDifferent(htmlCurrentEntity.getTitle_simhash(), previousEntity.getTitle_simhash()) == true) {
                                htmlCurrentEntity.setTitleSimhashChgInd(true);
                                isDifferent = true;
                            }
                        }
                        break;
                    }
                    // viewport_content
                    case IConstants.VIEWPORT_CONTENT: {
                        if (isDifferent(htmlCurrentEntity.getViewport_flag(), previousEntity.getViewport_flag()) == false) {
                            if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                                nextCurrentChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.VIEWPORT_CONTENT) == true) {
                                        currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextCurrentChangeTrackingHashCdJson;
                                    }
                                }
                            }
                            if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                                nextPreviousChangeTrackingHashCdJson:
                                for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                    if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.VIEWPORT_CONTENT) == true) {
                                        previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                        break nextPreviousChangeTrackingHashCdJson;
                                    }
                                }
                            }

                            if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                                htmlCurrentEntity.setViewportContentChgInd(true);
                                isDifferent = true;
                            }
                        }
                        break;
                    }
                    // viewport_flag
                    case IConstants.VIEWPORT_FLAG: {
                        if (isDifferent(htmlCurrentEntity.getViewport_flag(), previousEntity.getViewport_flag()) == true) {
                            htmlCurrentEntity.setViewportFlagChgInd(true);
                            isDifferent = true;

                            // viewport_added_ind (ie. current is true and previous is false)
                            if (BooleanUtils.isTrue(htmlCurrentEntity.getViewport_flag())
                                    && BooleanUtils.isFalse(previousEntity.getViewport_flag())) {
                                htmlCurrentEntity.setViewportAddedInd(true);
                            }
                            // viewport_removed_ind (ie. current is false and previous is true)
                            else if (BooleanUtils.isFalse(htmlCurrentEntity.getViewport_flag())
                                    && BooleanUtils.isTrue(previousEntity.getViewport_flag())) {
                                htmlCurrentEntity.setViewportRemovedInd(true);
                            }

                        }
                        break;
                    }
                    // base_tag
                    case IConstants.BASE_TAG: {
                        if (isDifferent(htmlCurrentEntity.getBase_tag_flag(), previousEntity.getBase_tag_flag()) == false
                                && isDifferent(htmlCurrentEntity.getBase_tag(), previousEntity.getBase_tag()) == true) {
                            htmlCurrentEntity.setBaseTagChgInd(true);
                            isDifferent = true;
                        }
                        break;
                    }
                    // base_tag_flag
                    case IConstants.BASE_TAG_FLAG: {

                        // No change when
                        // 1) current crawl result does not detect <base> tag (ie. base_tag_flag is false)
                        // 2) record created before the changes for https://www.wrike.com/open.htm?id=539211931 is null (ie. base_tag_flag is null)
                        if (BooleanUtils.isFalse(htmlCurrentEntity.getBase_tag_flag()) && previousEntity.getBase_tag_flag() == null) {
                            // No change
                        } else if (isDifferent(htmlCurrentEntity.getBase_tag_flag(), previousEntity.getBase_tag_flag()) == true) {

                            isDifferent = true;

                            // base_tag_added_ind (ie. current is true and previous is false)
                            if (BooleanUtils.isTrue(htmlCurrentEntity.getBase_tag_flag())
                                    && BooleanUtils.isFalse(previousEntity.getBase_tag_flag())) {
                                htmlCurrentEntity.setBaseTagAddedInd(true);
                            }
                            // base_tag_removed_ind (ie. current is false and previous is true)
                            else if (BooleanUtils.isFalse(htmlCurrentEntity.getBase_tag_flag())
                                    && BooleanUtils.isTrue(previousEntity.getBase_tag_flag())) {
                                htmlCurrentEntity.setBaseTagRemovedInd(true);
                            }

                        }
                        break;
                    }
                    // base_tag_target
                    case IConstants.BASE_TAG_TARGET: {
                        if (isDifferent(htmlCurrentEntity.getBase_tag_target(), previousEntity.getBase_tag_target()) == true) {
                            htmlCurrentEntity.setBaseTagTargetChgInd(true);
                            isDifferent = true;
                        }
                        break;
                    }
                    // page_analysis_fragments
                    case IConstants.PAGE_ANALYSIS_FRAGMENTS: {
                        if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
                            nextCurrentChangeTrackingHashCdJson:
                            for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
                                if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.PAGE_ANALYSIS_FRAGMENTS) == true) {
                                    currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                    break nextCurrentChangeTrackingHashCdJson;
                                }
                            }
                        }
                        if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
                            nextPreviousChangeTrackingHashCdJson:
                            for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
                                if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.PAGE_ANALYSIS_FRAGMENTS) == true) {
                                    previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
                                    break nextPreviousChangeTrackingHashCdJson;
                                }
                            }
                        }
                        if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
                            htmlCurrentEntity.setPageAnalysisFragmentsChgInd(true);
                            isDifferent = true;
                        }
                        break;
                    }
                    // response_headers (names)
                    case IConstants.RESPONSE_HEADERS: {
                        currentResponseHeaderNames = getResponseHeaderNames(htmlCurrentEntity.getResponse_headers());
                        //if (isDebug == true) {
                        // (tested) test case 1: current response header names is not empty, previous response header names is not empty, and current = previous, no change
                        // (tested) test case 2: current response header names is empty, previous response header names is not empty, response_headers_removed_ind = true
                        // (tested) test case 3: current response header names is not empty, previous response header names is empty,  response_headers_added_ind = true
                        // (tested) test case 4: current response header names is not empty, previous response header names is not empty, current header name added, response_headers_added_ind = true
                        // (tested) test case 5: current response header names is not empty, previous response header names is not empty, previous header name removed, response_headers_removed_ind = true
                        // (tested) test case 6: current response header names is not empty, previous response header names is not empty, current header name added, response_headers_added_ind = true
                        // (tested) test case 6: current response header names is not empty, previous response header names is not empty, previous header name removed, response_headers_removed_ind = true
                        //List<ResponseHeaders> testResponseHeaderList = new ArrayList<ResponseHeaders>(Arrays.asList(htmlCurrentEntity.getResponse_headers()));
                        //testResponseHeaderList.remove(0);
                        //ResponseHeaders responseHeaders = new ResponseHeaders();
                        //responseHeaders.setName("New-Response-Header-Name1");
                        //responseHeaders.setValue("New-Response-Header-Value1");
                        //testResponseHeaderList.add(responseHeaders);
                        //responseHeaders = new ResponseHeaders();
                        //responseHeaders.setName("New-Response-Header-Name2");
                        //responseHeaders.setValue("New-Response-Header-Value2");
                        //testResponseHeaderList.add(responseHeaders);
                        //htmlCurrentEntity.setResponse_headers(testResponseHeaderList.toArray(new ResponseHeaders[0]));
                        //currentResponseHeaderNames = getResponseHeaderNames(htmlCurrentEntity.getResponse_headers());
                        //if (currentResponseHeaderNames != null && currentResponseHeaderNames.length > 0) {
                        //	System.out.println("currentResponseHeaderNames=" + Arrays.asList(currentResponseHeaderNames).toString());
                        //} else {
                        //	System.out.println("currentResponseHeaderNames is empty.");
                        //}
                        //if (previousEntity.getResponse_header_names() != null && previousEntity.getResponse_header_names().length > 0) {
                        //	System.out.println("previousResponseHeaderNames=" + Arrays.asList(previousEntity.getResponse_header_names()).toString());
                        //} else {
                        //	System.out.println("previousResponseHeaderNames is empty.");
                        //}
                        //}
                        htmlCurrentEntity
                                .setResponseHeadersAddedInd(getResponseHeadersAddedInd(currentResponseHeaderNames, previousEntity.getResponse_header_names()));
                        htmlCurrentEntity.setResponseHeadersRemovedInd(
                                getResponseHeadersRemovedInd(currentResponseHeaderNames, previousEntity.getResponse_header_names()));

                        if (BooleanUtils.isTrue(htmlCurrentEntity.getResponseHeadersAddedInd())
                                || BooleanUtils.isTrue(htmlCurrentEntity.getResponseHeadersRemovedInd())) {
                            isDifferent = true;
                        }

                        //if (isDebug == true) {
                        //	System.out.println("responseHeadersAddedInd=" + htmlCurrentEntity.getResponseHeadersAddedInd() + ",responseHeadersRemovedInd="
                        //			+ htmlCurrentEntity.getResponseHeadersRemovedInd() + ",isDifferent=" + isDifferent);
                        //}

                        break;
                    }
                    default:
                        break;
                }
            }
        }

        return isDifferent;
    }

    private boolean checkIfCustomDataAvailable(String customData) {
        if (StringUtils.isBlank(customData)) {
            return false;
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(customData);
            if (jsonObject == null || jsonObject.isEmpty()) {
                return false;
            }

            // Check if content is not empty
            if (jsonObject.containsKey("content") && StringUtils.isNotBlank(jsonObject.getString("content"))) {
                return true;
            }

            // Check if links exist
            if (jsonObject.containsKey("links") && jsonObject.getJSONArray("links") != null
                    && !jsonObject.getJSONArray("links").isEmpty()) {
                return true;
            }

            // Check if word count is greater than 0
            if (jsonObject.containsKey("word_count") && jsonObject.getIntValue("word_count") > 0) {
                return true;
            }

            return false;
        } catch (Exception e) {
            // If parsing fails, log and return false
            return false;
        }
    }

    private Boolean getResponseHeadersAddedInd(String[] currentResponseHeaderNames, String[] previousResponseHeaderNames) {
        Boolean output = null;
        boolean isPrevious = false;

        // when no previous response headers but there are current response headers
        if ((previousResponseHeaderNames == null || previousResponseHeaderNames.length == 0) && currentResponseHeaderNames != null
                && currentResponseHeaderNames.length > 0) {
            output = true;
        }
        // when there are previous response headers and there are current response headers
        else if (previousResponseHeaderNames != null && previousResponseHeaderNames.length > 0 && currentResponseHeaderNames != null
                && currentResponseHeaderNames.length > 0) {
            // when current response header name not one of previous response names
            nextCurrentResponseHeaderName:
            for (String currentResponseHeaderName : currentResponseHeaderNames) {
                isPrevious = false;
                nextPreviousResponseHeaderName:
                for (String previousResponseHeaderName : previousResponseHeaderNames) {
                    if (StringUtils.equalsIgnoreCase(currentResponseHeaderName, previousResponseHeaderName)) {
                        isPrevious = true;
                        break nextPreviousResponseHeaderName;
                    }
                }
                if (isPrevious == false) {
                    output = true;
                    break nextCurrentResponseHeaderName;
                }
            }
        }

        return output;
    }

    private Boolean getResponseHeadersRemovedInd(String[] currentResponseHeaderNames, String[] previousResponseHeaderNames) {
        Boolean output = null;
        boolean isCurrent = false;

        // when there are previous response headers but no current response headers
        if (previousResponseHeaderNames != null && previousResponseHeaderNames.length > 0
                && (currentResponseHeaderNames == null || currentResponseHeaderNames.length == 0)) {
            output = true;
        }
        // when there are previous response headers and there are current response headers
        else if (previousResponseHeaderNames != null && previousResponseHeaderNames.length > 0 && currentResponseHeaderNames != null
                && currentResponseHeaderNames.length > 0) {
            // when previous response header name not one of current response names
            nextPreviousResponseHeaderName:
            for (String previousResponseHeaderName : previousResponseHeaderNames) {
                isCurrent = false;
                nextCurrentResponseHeaderName:
                for (String currentResponseHeaderName : currentResponseHeaderNames) {
                    if (StringUtils.equalsIgnoreCase(previousResponseHeaderName, currentResponseHeaderName)) {
                        isCurrent = true;
                        break nextCurrentResponseHeaderName;
                    }
                }
                if (isCurrent == false) {
                    output = true;
                    break nextPreviousResponseHeaderName;
                }
            }
        }

        return output;
    }

    // https://www.wrike.com/open.htm?id=400932490
    // expected output format: {"24":1, "31":1, "33":1}
    public String transformPageAnalysisResultsChgIndArrayToString(PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray) {
        String pageAnalysisResultsChgIndJson = null;
        JsonObject jsonObject = new JsonObject();
        int ruleNumber = 0;
        int changeIndicator = 0;
        if (pageAnalysisResultChgIndArray != null && pageAnalysisResultChgIndArray.length > 0) {
            for (PageAnalysisResultChgInd pageAnalysisResultChgInd : pageAnalysisResultChgIndArray) {
                ruleNumber = pageAnalysisResultChgInd.getRule_nbr();
                changeIndicator = pageAnalysisResultChgInd.getChg_ind();
                jsonObject.addProperty(String.valueOf(ruleNumber), changeIndicator);
            }
            pageAnalysisResultsChgIndJson = jsonObject.toString();
        }
        return pageAnalysisResultsChgIndJson;
    }

    public boolean isDifferent(Boolean value1, Boolean value2) {
        boolean output = false;
        if (value1 == null && value2 == null) {
            output = false;
        } else if (value1 != null && value2 == null) {
            output = true;
        } else if (value1 == null && value2 != null) {
            output = true;
        } else if (value1.booleanValue() != value2.booleanValue()) {
            output = true;
        }
        return output;
    }

    public boolean isDifferent(Integer value1, Integer value2) {
        boolean output = false;
        if (value1 == null && value2 == null) {
            output = false;
        } else if (value1 != null && value2 == null) {
            output = true;
        } else if (value1 == null && value2 != null) {
            output = true;
        } else if (value1.intValue() != value2.intValue()) {
            output = true;
        }
        return output;
    }

    public boolean isDifferent(String value1, String value2) {
        boolean output = false;
        if (StringUtils.isBlank(value1) && StringUtils.isBlank(value2)) {
            output = false;
        } else if (StringUtils.isBlank(value1) && StringUtils.isNotBlank(value2)) {
            output = true;
        } else if (StringUtils.isNotBlank(value1) && StringUtils.isBlank(value2)) {
            output = true;
        } else if (StringUtils.isNotBlank(value1) && StringUtils.isNotBlank(value2) && !StringUtils.equalsIgnoreCase(value1, value2)) {
            output = true;
        }
        return output;
    }

    public boolean isDifferent(BigInteger value1, BigInteger value2) {
        boolean output = false;
        if (value1 == null && value2 == null) {
            output = false;
        } else if (value1 != null && value2 == null) {
            output = true;
        } else if (value1 == null && value2 != null) {
            output = true;
        } else if (value1.compareTo(value2) != 0) {
            output = true;
        }
        return output;
    }

    public boolean isDifferent(Date value1, Date value2) {
        boolean output = false;
        String formattedDateString1 = null;
        String formattedDateString2 = null;
        if (value1 == null && value2 == null) {
            output = false;
        } else if (value1 != null && value2 == null) {
            output = true;
        } else if (value1 == null && value2 != null) {
            output = true;
        } else {
            formattedDateString1 = DateFormatUtils.format(value1, IConstants.DATE_FORMAT_YYYYMMDDHHMMSS);
            formattedDateString2 = DateFormatUtils.format(value2, IConstants.DATE_FORMAT_YYYYMMDDHHMMSS);
            if (!StringUtils.equalsIgnoreCase(formattedDateString1, formattedDateString2)) {
                output = true;
            }
        }
        return output;
    }

    public boolean isDifferent(String[] value1, String[] value2) {
        boolean output = false;
        StringBuilder stringBuilder1 = null;
        StringBuilder stringBuilder2 = null;
        String string1 = null;
        String string2 = null;
        if (value1 == null && value2 == null) {
            output = false;
        } else if (value1 != null && value2 == null) {
            output = true;
        } else if (value1 == null && value2 != null) {
            output = true;
        } else {
            stringBuilder1 = new StringBuilder();
            for (String testString : value1) {
                stringBuilder1.append(testString);
            }
            string1 = getSortedCharactersHashCode(stringBuilder1.toString());
            stringBuilder2 = new StringBuilder();
            for (String testString : value2) {
                stringBuilder2.append(testString);
            }
            string2 = getSortedCharactersHashCode(stringBuilder2.toString());
            if (StringUtils.equalsIgnoreCase(string1, string2) == false) {
                output = true;
            }
        }
        return output;
    }

    public PageAnalysisResultChgInd[] getPageAnalysisResultChgIndArray(String ip, String urlString, PageAnalysisResult[] currentPageAnalysisResultArray,
                                                                       PageAnalysisResult[] previousPageAnalysisResultArray) {
        PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray = null;
        PageAnalysisResultChgInd pageAnalysisResultChgInd = null;
        List<PageAnalysisResultChgInd> pageAnalysisResultChgIndList = new ArrayList<PageAnalysisResultChgInd>();
        boolean isMatchingRuleFound = false;
        boolean isResultIdentical = false;
        boolean isChangeFrom0To1 = false;
        boolean isChangeFrom1To0 = false;

        int totalCurrentPageAnalysisRules = currentPageAnalysisResultArray.length;
        int totalPreviousPageAnalysisRules = previousPageAnalysisResultArray.length;

        // when current total number of rules greater than or equal to previous total number of rules
        if (totalCurrentPageAnalysisRules >= totalPreviousPageAnalysisRules) {
            for (PageAnalysisResult currentPageAnalysisResult : currentPageAnalysisResultArray) {
                isMatchingRuleFound = false;
                isResultIdentical = false;
                isChangeFrom0To1 = false;
                isChangeFrom1To0 = false;
                nextPreviousPageAnalysisResult:
                for (PageAnalysisResult previousPageAnalysisResult : previousPageAnalysisResultArray) {
                    // when rule number exists in both current and previous results
                    if (currentPageAnalysisResult.getRule() == previousPageAnalysisResult.getRule()) {
                        isMatchingRuleFound = true;
                        // when current and previous result is identical
                        if (currentPageAnalysisResult.getResult() == previousPageAnalysisResult.getResult()) {
                            isResultIdentical = true;
                        } else if (previousPageAnalysisResult.getResult() == 0 && currentPageAnalysisResult.getResult() == 1) {
                            isChangeFrom0To1 = true;
                        } else if (previousPageAnalysisResult.getResult() == 1 && currentPageAnalysisResult.getResult() == 0) {
                            isChangeFrom1To0 = true;
                        }
                        break nextPreviousPageAnalysisResult;
                    }
                }

                // when current rule is not in previous result
                if (isMatchingRuleFound == false) {
                    pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
                    pageAnalysisResultChgInd.setRule_nbr(currentPageAnalysisResult.getRule());
                    pageAnalysisResultChgInd.setChg_ind(IConstants.TRUE_NUMERIC);
                    pageAnalysisResultChgIndList.add(pageAnalysisResultChgInd);
                }
                // when current rule is in previous result
                else {
                    // when current and previous result is not identical
                    if (isResultIdentical == false) {
                        pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
                        pageAnalysisResultChgInd.setRule_nbr(currentPageAnalysisResult.getRule());
                        if (isChangeFrom0To1 == true) {
                            pageAnalysisResultChgInd.setChg_ind(IConstants.TRUE_NUMERIC);
                        } else if (isChangeFrom1To0 == true) {
                            pageAnalysisResultChgInd.setChg_ind(IConstants.FALSE_NUMERIC);
                        }
                        pageAnalysisResultChgIndList.add(pageAnalysisResultChgInd);
                    }
                }
            }
        }
        // when current total number of rules less than previous total number of rules
        else {
            for (PageAnalysisResult previousPageAnalysisResult : previousPageAnalysisResultArray) {
                isMatchingRuleFound = false;
                isResultIdentical = false;
                isChangeFrom0To1 = false;
                isChangeFrom1To0 = false;
                nextCurrentPageAnalysisResult:
                for (PageAnalysisResult currentPageAnalysisResult : currentPageAnalysisResultArray) {
                    // when rule number exists in both previous and current results
                    if (previousPageAnalysisResult.getRule() == currentPageAnalysisResult.getRule()) {
                        isMatchingRuleFound = true;
                        // when previous and current result is identical
                        if (previousPageAnalysisResult.getResult() == currentPageAnalysisResult.getResult()) {
                            isResultIdentical = true;
                        } else if (previousPageAnalysisResult.getResult() == 0 && currentPageAnalysisResult.getResult() == 1) {
                            isChangeFrom0To1 = true;
                        } else if (previousPageAnalysisResult.getResult() == 1 && currentPageAnalysisResult.getResult() == 0) {
                            isChangeFrom1To0 = true;
                        }
                        break nextCurrentPageAnalysisResult;
                    }
                }

                // when previous rule is not in current result
                if (isMatchingRuleFound == false) {
                    pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
                    pageAnalysisResultChgInd.setRule_nbr(previousPageAnalysisResult.getRule());
                    pageAnalysisResultChgInd.setChg_ind(IConstants.TRUE_NUMERIC);
                    pageAnalysisResultChgIndList.add(pageAnalysisResultChgInd);
                }
                // when previous rule is in current result
                else {
                    // when previous and current result is not identical
                    if (isResultIdentical == false) {
                        pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
                        pageAnalysisResultChgInd.setRule_nbr(previousPageAnalysisResult.getRule());
                        if (isChangeFrom0To1 == true) {
                            pageAnalysisResultChgInd.setChg_ind(IConstants.TRUE_NUMERIC);
                        } else if (isChangeFrom1To0 == true) {
                            pageAnalysisResultChgInd.setChg_ind(IConstants.FALSE_NUMERIC);
                        }
                        pageAnalysisResultChgIndList.add(pageAnalysisResultChgInd);
                    }
                }
            }
        }

        if (pageAnalysisResultChgIndList != null && pageAnalysisResultChgIndList.size() > 0) {
            pageAnalysisResultChgIndArray = pageAnalysisResultChgIndList.toArray(new PageAnalysisResultChgInd[0]);
        }

        return pageAnalysisResultChgIndArray;
    }

    public String getHashCodeOfStringArray(String[] stringArray) {
        String hashCode = null;
        String json = null;
        if (stringArray != null && stringArray.length > 0) {
            json = gson.toJson(stringArray, String[].class);
            if (checkIfJsonDataAvailable(json) == true) {
                hashCode = getSortedCharactersHashCode(json);
            }
        }
        return hashCode;
    }

    private boolean checkIfJsonDataAvailable(String json) {
        return StringUtils.isNotBlank(json) && !"[]".equals(json);
    }

    public PageAnalysisResult[] getPageAnalysisResultArray(JSONObject jsonObject) {
        PageAnalysisResult[] pageAnalysisResultArray = null;
        List<PageAnalysisResult> pageAnalysisResultList = new ArrayList<PageAnalysisResult>();
        PageAnalysisResult pageAnalysisResult = null;
        int ruleNumber = 0;
        int ruleResult = 0;

        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_1_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_1_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_2_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_2_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_3_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_3_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_4_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_4_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_5_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_5_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_6_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_6_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_7_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_7_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_8_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_8_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_9_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_9_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_10_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_10_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_11_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_11_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_12_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_12_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_13_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_13_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_14_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_14_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_15_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_15_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_16_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_16_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_17_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_17_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_18_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_18_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_19_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_19_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_20_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_20_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_21_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_21_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_22_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_22_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_23_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_23_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_24_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_24_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_25_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_25_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_26_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_26_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_27_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_27_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_28_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_28_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_29_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_29_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_30_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_30_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_31_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_31_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_32_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_32_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_33_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_33_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_34_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_34_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_35_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_35_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_36_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_36_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_37_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_37_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_38_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_38_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_39_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_39_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_40_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_40_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_41_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_41_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_42_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_42_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_43_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_43_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_44_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_44_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_45_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_45_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_46_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_46_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_47_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_47_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_48_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_48_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_49_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_49_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_50_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_50_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_51_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_51_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_52_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_52_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_53_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_53_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_54_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_54_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_55_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_55_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_56_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_56_b").getBooleanValue("isIssue"));
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_57_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_57_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_58_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_58_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_59_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_59_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_60_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_60_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_61_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_61_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_62_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_62_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_63_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_63_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_64_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_64_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_65_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_65_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }

        // as of 2019-11-01, new rule number 66

        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_66_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_66_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_67_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_67_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_68_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_68_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_69_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_69_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_70_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_70_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_71_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_71_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_72_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_72_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_73_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_73_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_74_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_74_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_75_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_75_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_76_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_76_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_77_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_77_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_78_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_78_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_79_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_79_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_80_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_80_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_81_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_81_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_82_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_82_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_83_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_83_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_84_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_84_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_85_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_85_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_86_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_86_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_87_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_87_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_88_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_88_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_89_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_89_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_90_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_90_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_91_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_91_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_92_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_92_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_93_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_93_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_94_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_94_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_95_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_95_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_96_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_96_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_97_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_97_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_98_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_98_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_99_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_99_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_100_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_100_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_101_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_101_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_102_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_102_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_103_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_103_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_104_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_104_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }
        ruleNumber++;
        if (jsonObject.get("page_analysis_rule_105_b") != null) {
            ruleResult = BooleanUtils.toInteger(jsonObject.getJSONObject("page_analysis_rule_105_b").getBooleanValue("isIssue"));
            pageAnalysisResult = new PageAnalysisResult();
            pageAnalysisResult.setRule(ruleNumber);
            pageAnalysisResult.setResult(ruleResult);
            pageAnalysisResultList.add(pageAnalysisResult);
        }

        // as of 2019-12-19, maximum rule number 105

        pageAnalysisResultArray = pageAnalysisResultList.toArray(new PageAnalysisResult[0]);
        //FormatUtils.getInstance().logMemoryUsage("pageAnalysisResultArray=" + Arrays.toString(pageAnalysisResultArray));
        return pageAnalysisResultArray;
    }


}




