package seoclarity.actonia_competitor_summary.upload.politecrawl;

import java.util.Arrays;

public class PageAnalysisRule {
	private String[] fragments;
	private Boolean isIssue;
	private Integer ruleNbr;
	private Integer totalTrue;
	private Integer totalFalse;

	public String[] getFragments() {
		return fragments;
	}

	public void setFragments(String[] fragments) {
		this.fragments = fragments;
	}

	public Boolean getIsIssue() {
		return isIssue;
	}

	public void setIsIssue(Boolean isIssue) {
		this.isIssue = isIssue;
	}

	public Integer getRuleNbr() {
		return ruleNbr;
	}

	public void setRuleNbr(Integer ruleNbr) {
		this.ruleNbr = ruleNbr;
	}

	public Integer getTotalTrue() {
		return totalTrue;
	}

	public void setTotalTrue(Integer totalTrue) {
		this.totalTrue = totalTrue;
	}

	public Integer getTotalFalse() {
		return totalFalse;
	}

	public void setTotalFalse(Integer totalFalse) {
		this.totalFalse = totalFalse;
	}

	@Override
	public String toString() {
		return "PageAnalysisRule [fragments=" + Arrays.toString(fragments) + ", isIssue=" + isIssue + ", ruleNbr=" + ruleNbr + ", totalTrue=" + totalTrue
				+ ", totalFalse=" + totalFalse + "]";
	}

}
