package seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import seoclarity.actonia_competitor_summary.upload.politecrawl.FieldToChangeIdMapper;
import seoclarity.actonia_competitor_summary.upload.politecrawl.change.ChangeIndicatorEnum;

import java.time.LocalDate;
import java.util.*;


@Data
public class HtmlChange {
	private static final Logger log = LogManager.getLogger(HtmlChange.class);

	@JsonIgnore
	private int orderIndex;
	private static final Gson gson = new Gson();
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDate crawlDate;
	private int urlType;
	private int domainId;
	private String url;
	private String url_hash;
	private String url_murmur_hash;
	private int chgId;
	private String change_indicator;
	private String change_type;
	private String severity;
	private int criticalFlg;
	@JsonIgnore
	private int prevResponseCode;
	@JsonIgnore
	private int currResponseCode;
	private String response_code_current;
	private String response_code_previous;
	private String prevValue;
	private String currValue;
	private String current_crawl_timestamp;
	private String previous_crawl_timestamp;
	@JsonIgnore
	private Date prevCrawlTimestamp;
	@JsonIgnore
	private Date currCrawlTimestamp;
	@JsonIgnore
	private Date createTimestamp;
	@JsonIgnore
	private Map<String, String> previousChangeTrackingHashCdJsonMap;

	/**
	 * Enhanced method to build HtmlChange objects from two entities of the same type
	 * @param previousEntity Previous entity (same type as current)
	 * @param currentEntity Current entity
	 * @param fieldMappingJson JSON string containing field mappings
	 * @return List of HtmlChange objects
	 */
	public static List<HtmlChange> buildHtmlChangeFromEntities(HtmlClickHouseEntity previousEntity, HtmlClickHouseEntity currentEntity, String fieldMappingJson) throws Exception {
		List<HtmlChange> htmlChangeList = new ArrayList<>();

		// Parse JSON field mapping
		Map<String, Double> fieldToChangeIdMap = gson.fromJson(fieldMappingJson, Map.class);

		// Create base HtmlChange template
		HtmlChange template = createBaseHtmlChangeFromEntities(previousEntity, currentEntity);

		// Get unified field extractor
		UnifiedFieldExtractor extractor = new UnifiedFieldExtractor();

		// Process each field mapping
		for (Map.Entry<String, Double> entry : fieldToChangeIdMap.entrySet()) {
			String fieldName = entry.getKey();
			Integer changeId = entry.getValue().intValue();

			try {
				HtmlChange htmlChange = createHtmlChangeFromTemplate(template);
				htmlChange.setChgId(changeId);

				// Get previous and current values using unified extractor
				String prevValue = extractor.extractValue(previousEntity, fieldName);
				String currValue = extractor.extractValue(currentEntity, fieldName);

				// Only create change if values are different
				if (!Objects.equals(prevValue, currValue)) {
					htmlChange.setPrevValue(prevValue);
					htmlChange.setCurrValue(currValue);
					htmlChangeList.add(htmlChange);
				}

			} catch (Exception e) {
				log.error("Failed to process field: {}, changeId: {}, error: {}", fieldName, changeId, e.getMessage(), e);
			}
		}

		log.info("url: {}, htmlChangeList size: {}", currentEntity.getUrl(), htmlChangeList.size());
		return htmlChangeList;
	}

	/**
	 * Build HtmlChange objects from field values JSON (auto-detect change IDs from ChangeIndicatorEnum)
	 * @param previousEntity Previous entity (same type as current)
	 * @param currentEntity Current entity
	 * @param fieldValuesJson JSON string containing field names and their values
	 * @return List of HtmlChange objects
	 */
	public static List<HtmlChange> buildHtmlChangeFromFieldValues(HtmlClickHouseEntity previousEntity, HtmlClickHouseEntity currentEntity, String fieldValuesJson) throws Exception {
		List<HtmlChange> htmlChangeList = new ArrayList<>();

		// Parse JSON field values
		Map<String, Object> fieldValues = gson.fromJson(fieldValuesJson, Map.class);

		// Create base HtmlChange template
		HtmlChange template = createBaseHtmlChangeFromEntities(previousEntity, currentEntity);

		// Get unified field extractor and field mapper
		UnifiedFieldExtractor extractor = new UnifiedFieldExtractor();
		FieldToChangeIdMapper mapper = new FieldToChangeIdMapper();

		// Process each field in the JSON
		for (String fieldName : fieldValues.keySet()) {
			try {
				// Get the corresponding change ID from ChangeIndicatorEnum
				Integer changeId = mapper.getChangeIdForField(fieldName);
				if (changeId == null) {
					log.warn("No change ID mapping found for field: {}", fieldName);
					continue;
				}

				// Get previous and current values using unified extractor
				String prevValue = extractor.extractValue(previousEntity, fieldName);
				String currValue = extractor.extractValue(currentEntity, fieldName);

				// Only create change if values are different
				if (!Objects.equals(prevValue, currValue)) {
					HtmlChange htmlChange = createHtmlChangeFromTemplate(template);
					htmlChange.setChgId(changeId);
					htmlChange.setPrevValue(prevValue);
					htmlChange.setCurrValue(currValue);
					htmlChangeList.add(htmlChange);
				}

			} catch (Exception e) {
				log.error("Failed to process field: {}, error: {}", fieldName, e.getMessage(), e);
			}
		}

		log.info("url: {}, htmlChangeList size: {}", currentEntity.getUrl(), htmlChangeList.size());
		return htmlChangeList;
	}

	/**
	 * Create base HtmlChange template with common fields
	 */
	private static HtmlChange createBaseHtmlChange(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) throws Exception {
		final Date crawlDate = current.getCrawlDate();
		final Integer domainId = current.getDomainId();

		final ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = previous.getChangeTrackingHashCdJsonArray();
		final Map<String, String> previousChangeTrackingHashCdJsonMap = new HashMap<>();
		for (ChangeTrackingHashCdJson changeTrackingHashCdJson : changeTrackingHashCdJsonArray == null ? new ChangeTrackingHashCdJson[0] : changeTrackingHashCdJsonArray) {
			if (previousChangeTrackingHashCdJsonMap.put(changeTrackingHashCdJson.getName(), changeTrackingHashCdJson.getValue()) != null) {
				throw new IllegalStateException("Duplicate key");
			}
		}

		final Date previousCrawlTimestamp = DateUtils.parseDate(previous.getCrawl_timestamp(), new String[]{DATE_FORMAT_YYYY_MM_DD_HH_MM_SS});
		final Date currentCrawlTimestamp = current.getCrawlTimestamp();
		final Date createTimestamp = new Date();

		final HtmlChange template = new HtmlChange();
		template.setUrlType(previous.getUrlType());
		template.setCurrCrawlTimestamp(currentCrawlTimestamp);
		template.setPrevCrawlTimestamp(previousCrawlTimestamp);
		final String previousResponseCode = previous.getResponse_code();
		template.setPrevResponseCode(Integer.parseInt(previousResponseCode));
		template.setCurrResponseCode(current.getResponse_code());
		template.setCreateTimestamp(createTimestamp);
		template.setUrl(current.getUrl());
		template.setCrawlDate(crawlDate);
		template.setDomainId(domainId);
		template.setPreviousChangeTrackingHashCdJsonMap(previousChangeTrackingHashCdJsonMap);

		return template;
	}

	/**
	 * Create base HtmlChange template for two entities of the same type
	 */
	private static HtmlChange createBaseHtmlChangeFromEntities(HtmlClickHouseEntity previousEntity, HtmlClickHouseEntity currentEntity) throws Exception {
		final LocalDate crawlDate = currentEntity.getCrawlDate();
		final Integer domainId = currentEntity.getDomainId();

		final Date previousCrawlTimestamp = previousEntity.getCrawlTimestamp();
		final Date currentCrawlTimestamp = currentEntity.getCrawlTimestamp();
		final Date createTimestamp = new Date();

		final HtmlChange template = new HtmlChange();
		template.setUrlType(1); // Default for HtmlClickHouseEntity
		template.setCurrCrawlTimestamp(currentCrawlTimestamp);
		template.setPrevCrawlTimestamp(previousCrawlTimestamp);

		// Set response codes
		Integer prevResponseCode = previousEntity.getResponse_code();
		Integer currResponseCode = currentEntity.getResponse_code();
		template.setPrevResponseCode(prevResponseCode != null ? prevResponseCode : 0);
		template.setCurrResponseCode(currResponseCode != null ? currResponseCode : 0);

		template.setCreateTimestamp(createTimestamp);
		template.setUrl(currentEntity.getUrl());
		template.setCrawlDate(crawlDate);
		template.setDomainId(domainId);
		template.setPreviousChangeTrackingHashCdJsonMap(previousChangeTrackingHashCdJsonMap);

		return template;
	}

	/**
	 * Create a new HtmlChange from template
	 */
	private static HtmlChange createHtmlChangeFromTemplate(HtmlChange template) {
		final HtmlChange htmlChange = new HtmlChange();
		htmlChange.setCrawlDate(template.getCrawlDate());
		htmlChange.setUrlType(template.getUrlType());
		htmlChange.setDomainId(template.getDomainId());
		htmlChange.setUrl_hash(template.getUrl_hash());
		htmlChange.setUrl(template.getUrl());
		htmlChange.setPrevResponseCode(template.getPrevResponseCode());
		htmlChange.setCurrResponseCode(template.getCurrResponseCode());
		htmlChange.setPrevCrawlTimestamp(template.getPrevCrawlTimestamp());
		htmlChange.setCurrCrawlTimestamp(template.getCurrCrawlTimestamp());
		htmlChange.setCreateTimestamp(template.getCreateTimestamp());
		htmlChange.setPreviousChangeTrackingHashCdJsonMap(template.getPreviousChangeTrackingHashCdJsonMap());
		return htmlChange;
	}

	private static HtmlChange createHtmlChangeFromTemp(HtmlChange temp) {
		final HtmlChange htmlChange = new HtmlChange();
		htmlChange.setCrawlDate(temp.getCrawlDate());
		htmlChange.setUrlType(temp.getUrlType());
		htmlChange.setDomainId(temp.getDomainId());
		htmlChange.setUrl_hash(temp.getUrl_hash());
		htmlChange.setUrl(temp.getUrl());
		htmlChange.setPrevResponseCode(temp.getPrevResponseCode());
		htmlChange.setCurrResponseCode(temp.getCurrResponseCode());
		htmlChange.setPrevCrawlTimestamp(temp.getPrevCrawlTimestamp());
		htmlChange.setCurrCrawlTimestamp(temp.getCurrCrawlTimestamp());
		htmlChange.setCreateTimestamp(temp.getCreateTimestamp());
		htmlChange.setPreviousChangeTrackingHashCdJsonMap(temp.getPreviousChangeTrackingHashCdJsonMap());
		return htmlChange;
	}

	public static HtmlChange createFromChangeInd(TargetUrlChangeIndClickHouseEntity changeIndEntity) {
		HtmlChange htmlChange = new HtmlChange();
		try {
			final Date crawlDate = changeIndEntity.getCrawlDate();
			final Integer domainId = changeIndEntity.getDomainId();
			final String url = changeIndEntity.getUrl();
			final String urlHash = changeIndEntity.getUrlHash();
			htmlChange.setCrawlDate(crawlDate);
			htmlChange.setUrlType(URL_TYPE_MANAGED);
			htmlChange.setDomainId(domainId);
			htmlChange.setUrl(url);
			htmlChange.setUrl_hash(urlHash);
			final ChangeIndicatorEnum indicatorEnum = ChangeIndicatorEnum.fromIndicator(changeIndEntity.getChangeIndicator());
			htmlChange.setChgId(indicatorEnum.getId());
			final String md5Prev = indicatorEnum.getStrategy().convertPrevValue(changeIndEntity);
			final String md5Curr = indicatorEnum.getStrategy().convertCurrValue(changeIndEntity);
			htmlChange.setPrevValue(md5Prev);
			htmlChange.setCurrValue(md5Curr);
			final String responseCodePrevious = changeIndEntity.getResponseCodePrevious();
			if (StringUtils.isNotBlank(responseCodePrevious)) {
				try {
					htmlChange.setPrevResponseCode(Integer.parseInt(responseCodePrevious));
				} catch (NumberFormatException e) {
					log.error("changeInd: {} set prevResponseCode failed.\nHtmlChange: {}\n error: {}", changeIndEntity, htmlChange, e.getMessage(), e);
				}
			}
			final String responseCodeCurrent = changeIndEntity.getResponseCodeCurrent();
			if (StringUtils.isNotBlank(responseCodeCurrent)) {
				try {
					htmlChange.setCurrResponseCode(Integer.parseInt(responseCodeCurrent));
				} catch (NumberFormatException e) {
					log.error("changeInd: {} set currResponseCode failed.\nHtmlChange: {}\n error: {}", changeIndEntity, htmlChange, e.getMessage(), e);
				}
			}
			htmlChange.setPrevCrawlTimestamp(changeIndEntity.getPreviousCrawlTimestamp());
			htmlChange.setCurrCrawlTimestamp(changeIndEntity.getCurrentCrawlTimestamp());
			htmlChange.setCreateTimestamp(new Date());
		} catch (Exception e) {
			log.error("changeInd: {}\nHtmlChange: {}\n error: {}", changeIndEntity.getChangeIndicator(), htmlChange, e.getMessage(), e);
			throw new RuntimeException(e);
		}
		return htmlChange;
	}
}