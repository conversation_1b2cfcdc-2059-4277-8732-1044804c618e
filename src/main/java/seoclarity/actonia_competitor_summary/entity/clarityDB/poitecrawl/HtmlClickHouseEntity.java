package seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl;

import lombok.Data;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class HtmlClickHouseEntity {

	private LocalDate crawlDate;
	private LocalDateTime crawlTime;
	private Integer domainId;
	private String url;
	private BigInteger urlMurmurHash;
	private BigInteger browserMurmurHash;
	private String sumMd5;
	private String indicatorsMd5Json;

	private String urlDomain;
	private Integer weekOfYear;
	private String lowerCaseUrlHash;
	private String changeTrackingHash;
	private Date crawlTimestamp;
	private Date previousCrawlTimestamp;
	private String alternate_links;
	private Integer amphtml_flag;
	private String amphtml_href;
	private String analyzed_url_flg_s;
	private String analyzed_url_s;
	private String archive_flg;
	private String archive_flg_x_tag;
	private String blocked_by_robots;
	private String canonical;
	private Integer canonical_flg;
	private Integer canonical_header_flag;
	private String canonical_header_type;
	private String canonical_type;
	private String canonical_url_is_consistent;
	private String content_type;
	private String custom_data;
	private String description;
	private String description_flg;
	private Integer description_length;
	private Integer document_size;
	private String download_latency;
	private String download_time;
	private String follow_flg;
	private String follow_flg_x_tag;
	private String h1;
	private Integer h1_count;
	private String h1_flg;
	private Integer h1_length;
	private String h1_md5;
	private String h2;
	private Integer header_noarchive;
	private Integer header_nofollow;
	private Integer header_noindex;
	private Integer header_noodp;
	private Integer header_nosnippet;
	private Integer header_noydir;
	private String hreflang_errors;
	private String hreflang_links;
	private Integer hreflang_links_out_count;
	private Integer hreflang_url_count;
	private String index_flg;
	private String index_flg_x_tag;
	private Integer indexable;
	private String insecure_resources;
	private Integer insecure_resources_flag;
	private Integer long_redirect;
	private String meta_charset;
	private String meta_content_type;
	private Integer meta_disabled_sitelinks;
	private Integer meta_noodp;
	private Integer meta_nosnippet;
	private Integer meta_noydir;
	private Integer meta_redirect;
	private Integer mixed_redirects;
	private Integer mobile_rel_alternate_url_is_consistent;
	private Integer noodp;
	private Integer nosnippet;
	private Integer noydir;
	private String og_markup;
	private Integer og_markup_flag;
	private Integer og_markup_length;
	private Integer outlink_count;
	private Integer page_1;
	private String page_analysis_rule_10_b;
	private String page_analysis_rule_11_b;
	private String page_analysis_rule_12_b;
	private String page_analysis_rule_13_b;
	private String page_analysis_rule_14_b;
	private String page_analysis_rule_15_b;
	private String page_analysis_rule_16_b;
	private String page_analysis_rule_17_b;
	private String page_analysis_rule_18_b;
	private String page_analysis_rule_19_b;
	private String page_analysis_rule_1_b;
	private String page_analysis_rule_20_b;
	private String page_analysis_rule_21_b;
	private String page_analysis_rule_22_b;
	private String page_analysis_rule_23_b;
	private String page_analysis_rule_24_b;
	private String page_analysis_rule_25_b;
	private String page_analysis_rule_26_b;
	private String page_analysis_rule_27_b;
	private String page_analysis_rule_28_b;
	private String page_analysis_rule_29_b;
	private String page_analysis_rule_2_b;
	private String page_analysis_rule_30_b;
	private String page_analysis_rule_31_b;
	private String page_analysis_rule_32_b;
	private String page_analysis_rule_33_b;
	private String page_analysis_rule_34_b;
	private String page_analysis_rule_35_b;
	private String page_analysis_rule_36_b;
	private String page_analysis_rule_37_b;
	private String page_analysis_rule_38_b;
	private String page_analysis_rule_39_b;
	private String page_analysis_rule_3_b;
	private String page_analysis_rule_40_b;
	private String page_analysis_rule_41_b;
	private String page_analysis_rule_42_b;
	private String page_analysis_rule_43_b;
	private String page_analysis_rule_44_b;
	private String page_analysis_rule_45_b;
	private String page_analysis_rule_46_b;
	private String page_analysis_rule_47_b;
	private String page_analysis_rule_48_b;
	private String page_analysis_rule_49_b;
	private String page_analysis_rule_4_b;
	private String page_analysis_rule_50_b;
	private String page_analysis_rule_51_b;
	private String page_analysis_rule_52_b;
	private String page_analysis_rule_53_b;
	private String page_analysis_rule_54_b;
	private String page_analysis_rule_55_b;
	private String page_analysis_rule_56_b;
	private String page_analysis_rule_57_b;
	private String page_analysis_rule_58_b;
	private String page_analysis_rule_59_b;
	private String page_analysis_rule_5_b;
	private String page_analysis_rule_60_b;
	private String page_analysis_rule_61_b;
	private String page_analysis_rule_62_b;
	private String page_analysis_rule_63_b;
	private String page_analysis_rule_64_b;
	private String page_analysis_rule_65_b;
	private String page_analysis_rule_6_b;
	private String page_analysis_rule_7_b;
	private String page_analysis_rule_8_b;
	private String page_analysis_rule_9_b;
	private String page_link;
	private Integer page_timeout_flag;
	private Integer paginated;
	private String pagination_links;
	private String protocol;
	private Integer redirect_blocked;
	private String redirect_blocked_reason;
	private String redirect_chain;
	private String redirect_final_url;
	private Integer redirect_flg;
	private Integer redirect_times;
	private String rel_next_html_url;
	private Integer rel_next_url_is_consistent;
	private Integer rel_prev_url_is_consistent;
	private String request_headers;
	private String request_time;
	private Integer response_code;
	private String response_headers;
	private Integer retry_attempted;
	private String robots;
	private String robots_contents;
	private Integer robots_contents_x_tag;
	private String robots_flg;
	private String robots_flg_x_tag;
	private String source_url;
	private String splash_took;
	private String structured_data;
	private String title;
	private String title_flg;
	private Integer title_length;
	private String title_md5;
	private String title_simhash;
	private Integer twitter_description_length;
	private String twitter_markup;
	private Integer twitter_markup_flag;
	private Integer twitter_markup_length;
	private Integer url_length;
	private String valid_twitter_card;
	private String viewport_content;
	private Integer viewport_flag;
	private String page_analysis_rule_66_b;
	private String page_analysis_rule_67_b;
	private String page_analysis_rule_68_b;
	private String page_analysis_rule_69_b;
	private String page_analysis_rule_70_b;
	private String page_analysis_rule_71_b;
	private String page_analysis_rule_72_b;
	private String page_analysis_rule_73_b;
	private String page_analysis_rule_74_b;
	private String page_analysis_rule_75_b;
	private String page_analysis_rule_76_b;
	private String page_analysis_rule_77_b;
	private String page_analysis_rule_78_b;
	private String page_analysis_rule_79_b;
	private String page_analysis_rule_80_b;
	private String page_analysis_rule_81_b;
	private String page_analysis_rule_82_b;
	private String page_analysis_rule_83_b;
	private String page_analysis_rule_84_b;
	private String page_analysis_rule_85_b;
	private String page_analysis_rule_86_b;
	private String page_analysis_rule_87_b;
	private String page_analysis_rule_88_b;
	private String page_analysis_rule_89_b;
	private String page_analysis_rule_90_b;
	private String page_analysis_rule_91_b;
	private String page_analysis_rule_92_b;
	private String page_analysis_rule_93_b;
	private String page_analysis_rule_94_b;
	private String page_analysis_rule_95_b;
	private String page_analysis_rule_96_b;
	private String page_analysis_rule_97_b;
	private String page_analysis_rule_98_b;
	private String page_analysis_rule_99_b;
	private String page_analysis_rule_100_b;
	private String page_analysis_rule_101_b;
	private String page_analysis_rule_102_b;
	private String page_analysis_rule_103_b;
	private String page_analysis_rule_104_b;
	private String page_analysis_rule_105_b;
	private String file_name;
	private String base_tag;
	private Integer base_tag_flag;
	private String base_tag_target;

}